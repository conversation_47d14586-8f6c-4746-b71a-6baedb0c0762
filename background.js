// Import the main functions from Code.js
importScripts('Code.js');

// Listen for messages from the popup
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  if (request.action === 'generateImage') {
    // Always use the Imagen model regardless of what was passed
    const model = 'imagen-3.0-generate-002';
    console.log(`Generating image for dish: ${request.dishName} using Imagen model`);

    // Call the main function to get or generate the dish image
    getOrGenerateDishImage(request.dishName, request.ingredients, model)
      .then(result => {
        sendResponse(result);
      })
      .catch(error => {
        console.error('Error in getOrGenerateDishImage:', error);
        sendResponse({
          success: false,
          error: 'An error occurred while generating the image',
          details: error.message
        });
      });

    // Return true to indicate that the response will be sent asynchronously
    return true;
  } else if (request.action === 'getOrGenerateDishImage') {
    // Handle the same as generateImage - this is the main dish processing action
    const model = 'imagen-3.0-generate-002';
    console.log(`Processing dish image request: ${request.dishName} using Imagen model`);

    // Call the main function to get or generate the dish image
    getOrGenerateDishImage(request.dishName, request.ingredients, model)
      .then(result => {
        sendResponse(result);
      })
      .catch(error => {
        console.error('Error in getOrGenerateDishImage:', error);
        sendResponse({
          success: false,
          error: 'An error occurred while processing the dish image',
          details: error.message
        });
      });

    // Return true to indicate that the response will be sent asynchronously
    return true;
  } else if (request.action === 'searchImages') {
    // Search for images by dish name
    searchDishImagesByName(request.dishName)
      .then(result => {
        sendResponse(result);
      })
      .catch(error => {
        console.error('Error in searchDishImagesByName:', error);
        sendResponse({
          success: false,
          error: 'An error occurred while searching for images',
          details: error.message,
          dishName: request.dishName,
          count: 0,
          files: []
        });
      });

    // Return true to indicate that the response will be sent asynchronously
    return true;
  } else if (request.action === 'testCompression') {
    // Test image compression functionality
    console.log('Testing image compression...');

    testImageCompression(request.imageData, request.targetSizeKB || 100)
      .then(result => {
        sendResponse(result);
      })
      .catch(error => {
        console.error('Error in compression test:', error);
        sendResponse({
          success: false,
          error: 'An error occurred during compression test',
          details: error.message
        });
      });

    // Return true to indicate that the response will be sent asynchronously
    return true;
  } else if (request.action === 'testFirebaseStorage') {
    // Test Firebase Storage connection
    console.log('Testing Firebase Storage connection...');

    testFirebaseStorageConnection()
      .then(result => {
        sendResponse(result);
      })
      .catch(error => {
        console.error('Error in Firebase Storage test:', error);
        sendResponse({
          success: false,
          error: 'An error occurred during Firebase Storage test',
          details: error.message
        });
      });

    // Return true to indicate that the response will be sent asynchronously
    return true;
  }
});

// Initialize the extension
function initialize() {
  // Firebase Storage configuration is now hardcoded in Code.js (via CONFIG object).
  // No need to check or set them in chrome.storage.sync anymore.
  console.log('Extension initialized. Using Firebase Storage configuration from CONFIG.');

  // Firebase-only mode - no local cache initialization needed
  console.log('🔥 Firebase-only mode: Using Firebase Storage as single source of truth');
}

// Run initialization when the extension is installed or updated
chrome.runtime.onInstalled.addListener(function(details) {
  console.log('Extension installed or updated:', details.reason);
  initialize();
});

// Also run initialization when the background script loads
initialize();

// Log that the background script has loaded
console.log('Dish Image Generator background script loaded');
