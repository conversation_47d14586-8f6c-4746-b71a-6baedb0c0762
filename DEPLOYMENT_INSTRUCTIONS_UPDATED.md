# Updated Deployment Instructions - Firebase Storage Version

## 🔄 **What Changed**

### Firebase Storage Migration
- **Storage Backend**: Migrated from Google Drive to Firebase Storage
- **Version**: Updated to 1.4
- **OAuth Client**: New client ID for Firebase Storage scopes
- **Key**: Updated Chrome extension key

### Updated Configuration
- **New OAuth Client ID**: `************-1p894uce3afoa33mjv7s9vb46hv64l8v.apps.googleusercontent.com`
- **New Chrome Extension Key**: `MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuNXjm7r8+8Xg2PfQ5hEKvmx3esDeDD6DIthvd5nuLjDpqUWNvo7iHM29pBbnK3ngHkGoV6FlIh0GJcec3BvXExWZjzLDHNLcUQMHCtMYkl1purUIPRIDmIMDq35l13ixiV14lQ8dKIg8LGuDFIR39VDN4d1CbxgUiJz6VT6JL/dzQakvIx69y/OTbKE9pLwOLJTuNX6euC5bezgnfDq55PHC3MG6KqingFi+S/b4adlBnE8dnG6fTcpp9zOUya3EdSlk/iVvk4aAwJ94sGp4pY97sTIH9ONiGsirxYi4wDxN+hFh++l5rJ6GGPQTbSGh5Bg4dfmT2zVZefRQn6R1kwIDAQAB`
- **OAuth Scope**: `https://www.googleapis.com/auth/devstorage.read_write`

## 📦 **Quick Deployment Steps**

### 1. Pre-Deployment
```bash
# Increment version in manifest.json (1.4 → 1.5)
# Remove key temporarily from manifest.json
```

### 2. Package Extension
```bash
zip -r eat_extension_deployment.zip . -x "*.git*" "*.DS_Store" "node_modules/*" "*.log" "images/*"
```

### 3. Restore Key (IMMEDIATELY)
Add this key back to manifest.json line 6:
```json
"key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuNXjm7r8+8Xg2PfQ5hEKvmx3esDeDD6DIthvd5nuLjDpqUWNvo7iHM29pBbnK3ngHkGoV6FlIh0GJcec3BvXExWZjzLDHNLcUQMHCtMYkl1purUIPRIDmIMDq35l13ixiV14lQ8dKIg8LGuDFIR39VDN4d1CbxgUiJz6VT6JL/dzQakvIx69y/OTbKE9pLwOLJTuNX6euC5bezgnfDq55PHC3MG6KqingFi+S/b4adlBnE8dnG6fTcpp9zOUya3EdSlk/iVvk4aAwJ94sGp4pY97sTIH9ONiGsirxYi4wDxN+hFh++l5rJ6GGPQTbSGh5Bg4dfmT2zVZefRQn6R1kwIDAQAB"
```

## 🔧 **Current Extension Status**

### ✅ **Ready for Deployment**
- **Firebase Storage**: Fully implemented and configured
- **OAuth Client**: Updated for Firebase Storage scopes
- **Extension Key**: Updated and included in manifest
- **Version**: 1.4 (ready for increment to 1.5 for deployment)

### 🧪 **Testing Status**
- **Firebase Storage Connection**: Ready for testing
- **Image Upload/Download**: Implemented
- **Authentication**: New OAuth client configured
- **Caching**: Preserved and working

## 📋 **Deployment Checklist**

### Before Packaging
- [ ] Test Firebase Storage connection in options page
- [ ] Verify all functionality works with new OAuth client
- [ ] Increment version number (1.4 → 1.5)

### During Packaging
- [ ] Remove key from manifest.json
- [ ] Create deployment ZIP
- [ ] **IMMEDIATELY** restore key to manifest.json

### After Packaging
- [ ] Test extension still loads in development
- [ ] Verify ZIP package contents
- [ ] Upload to Chrome Web Store

## 🚨 **Important Notes**

1. **OAuth Client**: The new client ID `************-1p894uce3afoa33mjv7s9vb46hv64l8v.apps.googleusercontent.com` is configured for Firebase Storage scopes
2. **Extension Key**: Must be restored immediately after packaging to maintain development functionality
3. **Firebase Storage**: Images are now stored in `eat-with-images.appspot.com` bucket under `dish-images/` folder
4. **Backward Compatibility**: All existing cache and functionality preserved

## 📞 **Support**

For deployment assistance:
- See `DEPLOYMENT_GUIDE.md` for detailed instructions
- See `DEPLOYMENT_CHECKLIST.md` for step-by-step checklist
- All deployment documentation has been updated with new key and OAuth client
