{"manifest_version": 3, "name": "Eat (with images)", "version": "2.6-dev", "description": "Generate and store images of dishes using Google's Generative AI with Firebase Storage as single source of truth", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuNXjm7r8+8Xg2PfQ5hEKvmx3esDeDD6DIthvd5nuLjDpqUWNvo7iHM29pBbnK3ngHkGoV6FlIh0GJcec3BvXExWZjzLDHNLcUQMHCtMYkl1purUIPRIDmIMDq35l13ixiV14lQ8dKIg8LGuDFIR39VDN4d1CbxgUiJz6VT6JL/dzQakvIx69y/OTbKE9pLwOLJTuNX6euC5bezgnfDq55PHC3MG6KqingFi+S/b4adlBnE8dnG6fTcpp9zOUya3EdSlk/iVvk4aAwJ94sGp4pY97sTIH9ONiGsirxYi4wDxN+hFh++l5rJ6GGPQTbSGh5Bg4dfmT2zVZefRQn6R1kwIDAQAB", "permissions": ["storage", "identity", "activeTab", "tabs", "offscreen"], "host_permissions": ["https://www.googleapis.com/*", "https://generativelanguage.googleapis.com/*", "https://storage.googleapis.com/*"], "oauth2": {"client_id": "847291467714-oj6qpf2ct3ebjst2oudpvlersj721079.apps.googleusercontent.com", "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "action": {"default_popup": "popup.html", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "96": "icons/icon96.png", "128": "icons/icon128.png"}}, "icons": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "96": "icons/icon96.png", "128": "icons/icon128.png"}, "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["https://eat.googleplex.com/*", "file://*/*"], "js": ["compression.js", "Code.js", "content.js"], "run_at": "document_end"}], "options_page": "options.html"}