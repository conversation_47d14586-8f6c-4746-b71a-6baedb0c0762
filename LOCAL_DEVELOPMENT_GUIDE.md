# Local Development Guide

## Development Workflow

### 🔧 Local Development (Default)
For day-to-day development, focus on:

1. **Edit Files Directly**
   - Make changes to `content.js`, `Code.js`, `popup.js`, etc.
   - Update `manifest.json` version only when needed
   - Test changes immediately in Chrome

2. **Load Extension in Chrome**
   - Open Chrome → Extensions → Developer mode
   - Click "Load unpacked" → Select project folder
   - Or click "Reload" if already loaded

3. **Test Changes**
   - Navigate to eat.googleplex.com
   - Check browser console for logs
   - Test functionality directly

4. **Iterate Quickly**
   - Make changes → Reload extension → Test
   - No packaging or deployment needed

### 📦 Packaging (Only When Requested)
**DO NOT automatically create deployment packages unless explicitly asked.**

When packaging is requested:

1. **Remove Key from manifest.json**
   ```bash
   # Remove the "key" line from manifest.json
   ```

2. **Create Package**
   ```bash
   zip -r eat_extension_deployment_v[VERSION].zip . -x "*.git*" "*.DS_Store" "node_modules/*" "*.log" "images/*"
   ```

3. **Restore Key to manifest.json**
   ```bash
   # Add back the "key" line to manifest.json
   ```

## Development Best Practices

### ✅ Focus Areas
- **Functionality First**: Get features working locally
- **Console Debugging**: Use detailed logging for troubleshooting
- **Incremental Changes**: Small, testable changes
- **Version Control**: Track changes in git (not deployment packages)

### ❌ Avoid During Development
- **Auto-packaging**: Don't create .zip files automatically
- **Version Bumping**: Only increment version when significant milestone reached
- **Deployment Focus**: Don't worry about deployment until feature is complete

## Testing Workflow

### 1. Local Testing
```bash
# 1. Make changes to files
# 2. Reload extension in Chrome
# 3. Test on eat.googleplex.com
# 4. Check console logs
# 5. Iterate
```

### 2. Console Debugging
Key things to look for:
- `🔍 Found X .venue-dish-header elements on page`
- `📝 Extracted dish name: "Name" (using selector: ...)`
- `🚀 Auto-load enabled, processing page automatically...`
- `🔥 Processing: Dish Name`
- Firebase Storage operations

### 3. Common Issues
- **No dishes found**: Check CSS selectors
- **No auto-load**: Check auto-load setting in popup
- **Firebase errors**: Check authentication and permissions
- **Images not showing**: Check image insertion logic

## File Structure

### Core Files (Edit Frequently)
- `content.js` - Main page interaction logic
- `Code.js` - Firebase Storage and image generation
- `popup.js` - Extension popup functionality
- `background.js` - Service worker

### Configuration Files (Edit Occasionally)
- `manifest.json` - Extension configuration
- `popup.html` - Popup UI structure
- `options.html` - Options page

### Support Files (Rarely Edit)
- `compression.js` - Image compression utilities
- `offscreen.js` - Offscreen document for compression
- Icons and documentation

## Quick Commands

### Reload Extension
```bash
# In Chrome: Extensions → Developer mode → Reload button
```

### View Console Logs
```bash
# F12 → Console tab (on eat.googleplex.com)
# Look for extension logs with 🔥 🔍 📝 emojis
```

### Reset Extension State
```bash
# Chrome → Extensions → Remove extension → Load unpacked again
```

## Development Tips

### 1. Use Console Logs Liberally
```javascript
console.log('🔍 Debug info:', variable);
console.log('✅ Success:', result);
console.log('❌ Error:', error);
```

### 2. Test Edge Cases
- Pages with no dishes
- Dishes with special characters
- Network connectivity issues
- Firebase authentication problems

### 3. Incremental Development
- Get basic functionality working first
- Add features one at a time
- Test thoroughly before moving to next feature

### 4. Version Management
- Only bump version for significant changes
- Use semantic versioning (2.6 → 2.7 for features, 2.6.1 for fixes)
- Don't auto-increment during development

## When to Package

### Request Packaging When:
- Feature is complete and tested
- Ready for deployment to production
- Need to share with others
- Significant milestone reached

### Packaging Request Format:
"Please create a deployment package for version X.X with the current changes"

This keeps development focused on functionality rather than deployment mechanics.
