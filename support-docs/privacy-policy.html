<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Privacy Policy - Eat (with images)</title>
    <style>
        body {
            font-family: '<PERSON><PERSON>', <PERSON>l, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1 { color: #1a73e8; }
        h2 { color: #1a73e8; margin-top: 30px; }
        .last-updated { color: #666; font-style: italic; }
        .highlight { background-color: #f8f9fa; padding: 10px; border-left: 4px solid #1a73e8; }
    </style>
</head>
<body>
    <h1>Privacy Policy</h1>
    <p class="last-updated">Last updated: [DATE]</p>

    <div class="highlight">
        <p><strong>Important:</strong> This extension is designed for Google employees and works exclusively with eat.googleplex.com. All data processing occurs within Google's infrastructure.</p>
    </div>

    <h2>1. Information We Collect</h2>
    <h3>1.1 Automatically Collected Information</h3>
    <ul>
        <li><strong>Dish Names:</strong> Names of dishes from eat.googleplex.com for image generation</li>
        <li><strong>Usage Data:</strong> Extension usage patterns and performance metrics</li>
        <li><strong>Cache Data:</strong> Generated images stored in your Google Drive</li>
    </ul>

    <h3>1.2 Google Account Information</h3>
    <ul>
        <li><strong>Authentication:</strong> Google account credentials for OAuth authentication</li>
        <li><strong>Drive Access:</strong> Permission to store and retrieve cached images</li>
        <li><strong>Cloud Platform Access:</strong> Access to Google's AI services for image generation</li>
    </ul>

    <h2>2. How We Use Your Information</h2>
    <p>We use collected information to:</p>
    <ul>
        <li>Generate AI images of dishes using Google's Imagen technology</li>
        <li>Cache images in your Google Drive for faster loading</li>
        <li>Improve extension performance and user experience</li>
        <li>Provide technical support when needed</li>
    </ul>

    <h2>3. Data Storage and Security</h2>
    <h3>3.1 Storage Locations</h3>
    <ul>
        <li><strong>Generated Images:</strong> Stored in your personal Google Drive</li>
        <li><strong>Cache Data:</strong> Stored locally in your browser</li>
        <li><strong>Processing Data:</strong> Temporarily processed through Google Cloud Platform</li>
    </ul>

    <h3>3.2 Security Measures</h3>
    <ul>
        <li>All data transmission uses HTTPS encryption</li>
        <li>OAuth 2.0 authentication with Google services</li>
        <li>No data stored on external servers outside Google's infrastructure</li>
        <li>Access limited to eat.googleplex.com domain only</li>
    </ul>

    <h2>4. Data Sharing and Disclosure</h2>
    <p>We do not share your personal information with third parties except:</p>
    <ul>
        <li><strong>Google Services:</strong> Integration with Google Drive and Cloud Platform as necessary for functionality</li>
        <li><strong>Legal Requirements:</strong> When required by law or to protect rights and safety</li>
        <li><strong>Google Internal:</strong> As part of Google's internal dining platform ecosystem</li>
    </ul>

    <h2>5. Your Rights and Choices</h2>
    <h3>5.1 Data Control</h3>
    <ul>
        <li><strong>Access:</strong> View cached images in your Google Drive</li>
        <li><strong>Delete:</strong> Remove cached images from your Drive at any time</li>
        <li><strong>Revoke:</strong> Revoke extension permissions through Google account settings</li>
    </ul>

    <h3>5.2 Extension Controls</h3>
    <ul>
        <li>Clear local cache through extension options</li>
        <li>Disable automatic image generation</li>
        <li>Uninstall the extension to stop all data collection</li>
    </ul>

    <h2>6. Google Services Integration</h2>
    <p>This extension integrates with Google services:</p>
    <ul>
        <li><strong>Google Drive API:</strong> For image storage and retrieval</li>
        <li><strong>Google Cloud Platform:</strong> For AI image generation</li>
        <li><strong>Google OAuth:</strong> For secure authentication</li>
    </ul>
    <p>Your use of these services is also governed by Google's Privacy Policy.</p>

    <h2>7. Data Retention</h2>
    <ul>
        <li><strong>Cached Images:</strong> Retained in your Google Drive until you delete them</li>
        <li><strong>Local Data:</strong> Cleared when you uninstall the extension</li>
        <li><strong>Usage Logs:</strong> Retained for 90 days for debugging purposes</li>
    </ul>

    <h2>8. Children's Privacy</h2>
    <p>This extension is intended for Google employees and is not designed for use by children under 13.</p>

    <h2>9. International Data Transfers</h2>
    <p>Data processing occurs within Google's global infrastructure with appropriate safeguards in place.</p>

    <h2>10. Changes to This Policy</h2>
    <p>We may update this Privacy Policy periodically. Continued use of the extension after changes constitutes acceptance of the updated policy.</p>

    <h2>11. Contact Us</h2>
    <p>For privacy-related questions or concerns:</p>
    <ul>
        <li><strong>Email:</strong> <EMAIL></li>
        <li><strong>Subject:</strong> Eat (with images) Privacy Inquiry</li>
    </ul>

    <h2>12. Compliance</h2>
    <p>This extension complies with:</p>
    <ul>
        <li>Google's internal privacy standards</li>
        <li>Chrome Web Store privacy requirements</li>
        <li>Google Workspace Marketplace privacy policies</li>
    </ul>
</body>
</html>
