document.addEventListener('DOMContentLoaded', () => {
    const promptInput = document.getElementById('promptInput');
    const generateBtn = document.getElementById('generateBtn');
    const imageContainer = document.getElementById('imageContainer');
    const errorContainer = document.getElementById('errorContainer');
    const modelSelect = document.getElementById('modelSelect');

    const BASE_PROMPT = "professional photo of {dish_name} made with {ingredients} on a table in a restaurant. Natural lighting, close-up shot from a slightly high angle. shallow dof. no distractions"; // Keep BASE_PROMPT if still needed client-side, otherwise remove

    generateBtn.addEventListener('click', () => { // Changed to not be async as google.script.run is async
        const userInput = promptInput.value.trim();
        if (!userInput) {
            displayError('Please enter a prompt.');
            return;
        }

        imageContainer.innerHTML = ''; // Clear previous image
        errorContainer.innerHTML = ''; // Clear previous errors
        generateBtn.disabled = true;
        generateBtn.textContent = 'Generating...';

        const selectedModel = modelSelect.value;
        const dishName = userInput; // Assuming user input is dish name
        const ingredients = ''; // Assuming no ingredients input for now

        // Call the server-side Google Apps Script function
        google.script.run
            .withSuccessHandler(handleSuccess)
            .withFailureHandler(handleFailure)
            .getOrGenerateDishImage(dishName, ingredients, selectedModel);
            
    });

    function handleSuccess(response) {
        generateBtn.disabled = false;
        generateBtn.textContent = 'Generate Image';

        if (response.success) {
            const img = document.createElement('img');
            img.src = `data:${response.mimeType};base64,${response.imageData}`;
            imageContainer.appendChild(img);
            console.log(`Image for ${response.dishName} loaded from ${response.source}`);
        } else {
            displayError(response.error + (response.details ? `: ${response.details}` : ''));
        }
    }

    function handleFailure(error) {
        generateBtn.disabled = false;
        generateBtn.textContent = 'Generate Image';
        console.error('Script error:', error);
        displayError('An error occurred: ' + error.message);
    }

    function displayError(message) {
        errorContainer.textContent = message;
    }
});
