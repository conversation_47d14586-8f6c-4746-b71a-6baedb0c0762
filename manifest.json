{"manifest_version": 3, "name": "Eat (with images)", "version": "1.4", "description": "Generate and cache images of dishes using Google's Generative AI with Firebase Storage", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAv+i/FGLF/2fYNW4EVu/zAXws9tUzQfnmMJQmgRozC1uDuC8/h8Kku+/TiC01yVePody1i+KuyNIVb7Hh6+G6RDtDRdDzCDlk/Ebn7SpT+B0nmWz83tzIj652OH78B7Z40oaeFOyczYkIpiqLgqeGM/gSfx5hyhSeJU5zn56Eja53GFwV5MTE3VTt2VhOiSbVm7bpTg5CoNMhqqaWtB3dnZoUWGJQG5X/ttozCfVVeY9LPj+LsDgD4A3eLnpOUT0vHN2lFOCHrbxPLOR5lQRyKwtUxiAFglpTQwiPpWv6kJKrj2lfLyW2MYT6o1eCVTp0hKblEPEJU9wPYZ/hoQx7jwIDAQAB", "permissions": ["storage", "identity", "activeTab", "tabs", "offscreen"], "host_permissions": ["https://www.googleapis.com/*", "https://generativelanguage.googleapis.com/*", "https://storage.googleapis.com/*"], "oauth2": {"client_id": "847291467714-oj6qpf2ct3ebjst2oudpvlersj721079.apps.googleusercontent.com", "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "action": {"default_popup": "popup.html", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "96": "icons/icon96.png", "128": "icons/icon128.png"}}, "icons": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "96": "icons/icon96.png", "128": "icons/icon128.png"}, "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["https://eat.googleplex.com/*", "file://*/*"], "js": ["compression.js", "Code.js", "content.js"], "run_at": "document_end"}], "options_page": "options.html"}