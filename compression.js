// Image Compression Utility for Chrome Extension
// This file contains compression functions that require DOM APIs (Image, Canvas)
// It can be used in content scripts or injected into pages where DOM APIs are available

/**
 * Compress image data using canvas and JPEG compression
 * @param {string} base64Data - Base64 encoded image data (without data: prefix)
 * @param {number} targetSizeKB - Target size in KB
 * @returns {Promise<Object>} Compression result
 */
function compressImageWithCanvas(base64Data, targetSizeKB = 100) {
  return new Promise((resolve) => {
    console.log(`[compressImageWithCanvas] Starting compression. Target: ${targetSizeKB}KB. Input data length: ${base64Data ? base64Data.length : 'null'}`);
    if (typeof base64Data !== 'string' || base64Data.length === 0) {
      console.error('[compressImageWithCanvas] Error: Input base64Data is not a valid string or is empty.');
      resolve({
        success: false,
        error: 'Invalid input base64Data',
        details: 'Input data was not a string or was empty.',
        fallbackData: base64Data, // return original if it exists, even if invalid type
        fallbackMimeType: 'image/png'
      });
      return;
    }

    try {
      // Create an image element to work with
      const img = new Image();
      
      img.onload = function() {
        console.log(`[compressImageWithCanvas] img.onload triggered. Original dimensions: ${img.width}x${img.height}`);
        try {
          // Create a canvas to resize/compress the image
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');

          // Calculate new dimensions (max 800px width/height to reduce size)
          const maxDimension = 800;
          let { width, height } = img;
          const originalWidth = width;
          const originalHeight = height;

          if (width > maxDimension || height > maxDimension) {
            if (width > height) {
              height = (height * maxDimension) / width;
              width = maxDimension;
            } else {
              width = (width * maxDimension) / height;
              height = maxDimension;
            }
          }

          canvas.width = width;
          canvas.height = height;

          // Draw and compress the image
          ctx.drawImage(img, 0, 0, width, height);
          console.log(`[compressImageWithCanvas] Image drawn to canvas. New dimensions: ${width}x${height}`);

          // Try different quality levels to hit target size
          let quality = 0.8;
          let compressedDataUrl = canvas.toDataURL('image/jpeg', quality);
          
          if (!compressedDataUrl || compressedDataUrl === "data:,") {
            console.error('[compressImageWithCanvas] Error: canvas.toDataURL returned empty or invalid data on first attempt.');
            resolve({
              success: false,
              error: 'Canvas toDataURL failed initially',
              details: 'canvas.toDataURL returned empty or invalid data.',
              fallbackData: base64Data,
              fallbackMimeType: 'image/png'
            });
            return;
          }
          console.log(`[compressImageWithCanvas] Initial compression (quality ${quality}): data length ${compressedDataUrl.length}`);

          const initialQuality = quality;
          let attempts = 0;
          const maxAttempts = 8; // Prevent infinite loops

          // Reduce quality if still too large
          while (compressedDataUrl.length > targetSizeKB * 1024 * 1.1 && quality > 0.3 && attempts < maxAttempts) { // Added 1.1 buffer for base64 overhead
            quality = Math.max(0.1, quality - 0.1); // Ensure quality doesn't go below 0.1
            console.log(`[compressImageWithCanvas] Attempt ${attempts + 1}: Reducing quality to ${quality.toFixed(1)}`);
            compressedDataUrl = canvas.toDataURL('image/jpeg', quality);
            if (!compressedDataUrl || compressedDataUrl === "data:,") {
              console.error(`[compressImageWithCanvas] Error: canvas.toDataURL returned empty or invalid data during quality reduction (quality: ${quality}).`);
              resolve({
                success: false,
                error: 'Canvas toDataURL failed during quality reduction',
                details: `canvas.toDataURL returned empty data at quality ${quality}.`,
                fallbackData: base64Data,
                fallbackMimeType: 'image/png'
              });
              return;
            }
            attempts++;
          }
          console.log(`[compressImageWithCanvas] Final quality: ${quality.toFixed(1)}, Attempts: ${attempts}`);

          const originalSizeKB = Math.round(base64Data.length / 1024);
          // Estimate size from base64 string (length * 3/4), then remove data URL prefix for actual data
          const compressedBase64 = compressedDataUrl.split(',')[1];
          if (!compressedBase64) {
            console.error('[compressImageWithCanvas] Error: Failed to extract base64 data from compressedDataUrl.');
            resolve({
              success: false,
              error: 'Failed to extract base64 from compressed data URL',
              details: 'Compressed data URL did not contain base64 part.',
              fallbackData: base64Data,
              fallbackMimeType: 'image/png'
            });
            return;
          }
          const compressedSizeKB = Math.round(compressedBase64.length / 1024);
          const compressionRatio = Math.round((1 - compressedSizeKB / originalSizeKB) * 100);

          console.log(`[compressImageWithCanvas] 🗜️ Image compressed: ${originalSizeKB}KB → ${compressedSizeKB}KB (${compressionRatio}% reduction)`);
          console.log(`[compressImageWithCanvas] 🎛️ Quality used: ${Math.round(quality * 100)}% (started at ${Math.round(initialQuality * 100)}%)`);
          console.log(`[compressImageWithCanvas] 📐 Dimensions: ${originalWidth}x${originalHeight} → ${width}x${height}`);

          resolve({
            success: true,
            data: compressedBase64,
            mimeType: 'image/jpeg',
            originalSize: originalSizeKB,
            compressedSize: compressedSizeKB,
            quality: Math.round(quality * 100),
            initialQuality: Math.round(initialQuality * 100),
            dimensionsReduced: width < originalWidth || height < originalHeight,
            qualityReduced: quality < initialQuality,
            compressionRatio: compressionRatio,
            targetMet: compressedSizeKB <= targetSizeKB,
            attempts: attempts,
            originalDimensions: { width: originalWidth, height: originalHeight },
            finalDimensions: { width: width, height: height }
          });
        } catch (canvasError) {
          console.error('Canvas compression error:', canvasError);
          resolve({
            success: false,
            error: 'Canvas compression failed',
            details: canvasError.message,
            fallbackData: base64Data, // Return original data on error
            fallbackMimeType: 'image/png'
          });
        }
      };

      img.onerror = function(errorEvent) { // Changed 'error' to 'errorEvent' to avoid conflict
        console.error('[compressImageWithCanvas] img.onerror triggered.', errorEvent);
        let details = 'Could not load image for compression.';
        if (errorEvent && typeof errorEvent === 'object' && errorEvent.type) {
            details += ` Event type: ${errorEvent.type}.`;
        } else if (typeof errorEvent === 'string') {
            details += ` Message: ${errorEvent}`;
        }
        resolve({
          success: false,
          error: 'Image loading failed',
          details: details,
          fallbackData: base64Data,
          fallbackMimeType: 'image/png'
        });
      };

      // Load the image
      // Assuming input is PNG, but could be other formats if generation changes.
      // For robustness, try to infer or ensure it's a common format.
      // However, the primary source is image generation which should be controlled.
      console.log('[compressImageWithCanvas] Setting img.src...');
      img.src = `data:image/png;base64,${base64Data}`; // Sticking to PNG assumption from original code
      
    } catch (setupError) { // Changed 'error' to 'setupError'
      console.error('[compressImageWithCanvas] Compression setup error:', setupError);
      resolve({
        success: false,
        error: 'Compression setup failed',
        details: error.message,
        fallbackData: base64Data,
        fallbackMimeType: 'image/png'
      });
    }
  });
}

/**
 * Test image compression functionality
 * @param {string} base64Data - Base64 encoded image data
 * @param {number} targetSizeKB - Target size in KB
 * @returns {Promise<Object>} Test results
 */
async function testImageCompression(base64Data, targetSizeKB = 100) {
  try {
    console.log(`🧪 Testing compression with target size: ${targetSizeKB}KB`);
    console.log(`📊 Original image size: ${Math.round(base64Data.length / 1024)}KB`);

    const compressionResult = await compressImageWithCanvas(base64Data, targetSizeKB);
    
    if (compressionResult.success) {
      const compressionWorked = compressionResult.data !== base64Data && 
                               compressionResult.compressedSize < compressionResult.originalSize;
      
      console.log(`🗜️ Compression test results:`);
      console.log(`   Original: ${compressionResult.originalSize}KB`);
      console.log(`   Compressed: ${compressionResult.compressedSize}KB`);
      console.log(`   Reduction: ${compressionResult.compressionRatio}%`);
      console.log(`   Target met: ${compressionResult.targetMet ? 'Yes' : 'No'}`);
      console.log(`   Compression worked: ${compressionWorked ? 'Yes' : 'No'}`);
      
      return {
        success: true,
        compressionWorked: compressionWorked,
        compressedData: compressionResult.data,
        compressedMimeType: compressionResult.mimeType,
        ...compressionResult
      };
    } else {
      return {
        success: false,
        compressionWorked: false,
        error: compressionResult.error,
        details: compressionResult.details
      };
    }
  } catch (error) {
    console.error('❌ Compression test failed:', error);
    return {
      success: false,
      compressionWorked: false,
      error: 'Compression test failed',
      details: error.message
    };
  }
}

// Export functions for use in other contexts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    compressImageWithCanvas,
    testImageCompression
  };
}

// Make functions available globally for content script usage
if (typeof window !== 'undefined') {
  window.compressImageWithCanvas = compressImageWithCanvas;
  window.testImageCompression = testImageCompression;
}
