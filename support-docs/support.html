<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Support - Eat (with images)</title>
    <style>
        body {
            font-family: '<PERSON><PERSON>', <PERSON>l, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1 { color: #1a73e8; }
        h2 { color: #1a73e8; margin-top: 30px; }
        .faq-item { margin-bottom: 20px; }
        .faq-question { font-weight: bold; color: #1a73e8; }
        .contact-box { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .troubleshooting { background-color: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107; }
    </style>
</head>
<body>
    <h1>Eat (with images) Support</h1>
    <p>Get help with the Eat (with images) Chrome extension for eat.googleplex.com</p>

    <h2>Quick Start Guide</h2>
    <ol>
        <li><strong>Install:</strong> Add the extension from Chrome Web Store</li>
        <li><strong>Authenticate:</strong> Sign in with your Google account when prompted</li>
        <li><strong>Visit:</strong> Go to eat.googleplex.com</li>
        <li><strong>Enjoy:</strong> See AI-generated images appear automatically for dishes</li>
    </ol>

    <h2>Features</h2>
    <ul>
        <li><strong>Automatic Image Generation:</strong> AI creates images for dishes on eat.googleplex.com</li>
        <li><strong>Smart Caching:</strong> Images are saved to your Google Drive for faster loading</li>
        <li><strong>Popup Controls:</strong> Access settings and controls via the extension icon</li>
        <li><strong>Options Page:</strong> Configure extension behavior and clear cache</li>
    </ul>

    <h2>Frequently Asked Questions</h2>

    <div class="faq-item">
        <div class="faq-question">Q: Why am I not seeing any images?</div>
        <p>A: Make sure you're signed in to your Google account and have granted the necessary permissions. The extension only works on eat.googleplex.com.</p>
    </div>

    <div class="faq-item">
        <div class="faq-question">Q: How do I clear the image cache?</div>
        <p>A: Click the extension icon and select "Clear Cache" or go to the Options page via right-click → Options.</p>
    </div>

    <div class="faq-item">
        <div class="faq-question">Q: Where are the images stored?</div>
        <p>A: Generated images are cached in your personal Google Drive in a dedicated folder for faster access.</p>
    </div>

    <div class="faq-item">
        <div class="faq-question">Q: Can I use this extension on other websites?</div>
        <p>A: No, this extension is specifically designed for eat.googleplex.com and will only work on that domain.</p>
    </div>

    <div class="faq-item">
        <div class="faq-question">Q: How do I disable automatic image generation?</div>
        <p>A: Open the extension popup and uncheck "Auto-generate images" or visit the Options page for more settings.</p>
    </div>

    <h2>Troubleshooting</h2>

    <div class="troubleshooting">
        <h3>Common Issues and Solutions</h3>

        <h4>Images Not Loading</h4>
        <ul>
            <li>Check your internet connection</li>
            <li>Ensure you're signed in to Google</li>
            <li>Try refreshing the page</li>
            <li>Clear the extension cache and try again</li>
        </ul>

        <h4>Authentication Problems</h4>
        <ul>
            <li>Sign out and sign back in to Google</li>
            <li>Check that you've granted Drive and Cloud Platform permissions</li>
            <li>Try removing and re-adding the extension</li>
        </ul>

        <h4>Slow Performance</h4>
        <ul>
            <li>Clear the local cache to free up space</li>
            <li>Check your Google Drive storage quota</li>
            <li>Disable other extensions temporarily to test</li>
        </ul>

        <h4>Extension Not Working</h4>
        <ul>
            <li>Make sure you're on eat.googleplex.com</li>
            <li>Check that the extension is enabled in Chrome</li>
            <li>Try disabling and re-enabling the extension</li>
            <li>Update Chrome to the latest version</li>
        </ul>
    </div>

    <h2>Permissions Explained</h2>
    <p>The extension requires these permissions:</p>
    <ul>
        <li><strong>Google Drive:</strong> To store and retrieve cached images</li>
        <li><strong>Cloud Platform:</strong> To access Google's AI image generation services</li>
        <li><strong>Active Tab:</strong> To interact with eat.googleplex.com</li>
        <li><strong>Storage:</strong> To save settings and cache data locally</li>
    </ul>

    <h2>Privacy and Security</h2>
    <ul>
        <li>All data stays within Google's infrastructure</li>
        <li>Images are stored in your personal Google Drive</li>
        <li>No data is shared with third parties</li>
        <li>Extension only works on eat.googleplex.com</li>
    </ul>

    <h2>System Requirements</h2>
    <ul>
        <li><strong>Browser:</strong> Google Chrome (latest version recommended)</li>
        <li><strong>Account:</strong> Google account with Drive access</li>
        <li><strong>Network:</strong> Access to eat.googleplex.com</li>
        <li><strong>Permissions:</strong> Google Drive and Cloud Platform access</li>
    </ul>

    <div class="contact-box">
        <h2>Contact Support</h2>
        <p>Need additional help? Contact us:</p>
        <ul>
            <li><strong>Email:</strong> <EMAIL></li>
            <li><strong>Subject Line:</strong> Eat (with images) Support</li>
            <li><strong>Include:</strong>
                <ul>
                    <li>Chrome version</li>
                    <li>Extension version</li>
                    <li>Description of the issue</li>
                    <li>Steps you've already tried</li>
                </ul>
            </li>
        </ul>
        <p><strong>Response Time:</strong> We typically respond within 1-2 business days.</p>
    </div>

    <h2>Version Information</h2>
    <p><strong>Current Version:</strong> 1.1</p>
    <p><strong>Last Updated:</strong> [DATE]</p>
    <p><strong>Compatibility:</strong> Chrome 88+</p>

    <h2>Additional Resources</h2>
    <ul>
        <li><a href="privacy-policy.html">Privacy Policy</a></li>
        <li><a href="terms-of-service.html">Terms of Service</a></li>
        <li><a href="https://chrome.google.com/webstore">Chrome Web Store</a></li>
        <li><a href="https://workspace.google.com/marketplace">Google Workspace Marketplace</a></li>
    </ul>
</body>
</html>
