document.addEventListener('DOMContentLoaded', function() {
  const testAllThingsBtn = document.getElementById('testAllThingsBtn');
  const statusDiv = document.getElementById('status');
  const testImageResult = document.getElementById('testImageResult');
  const testImageStatus = document.getElementById('testImageStatus');
  const testImage = document.getElementById('testImage');
  const testCompressionResult = document.getElementById('testCompressionResult');
  const testCompressionStatus = document.getElementById('testCompressionStatus');
  const originalImage = document.getElementById('originalImage');
  const compressedImage = document.getElementById('compressedImage');
  const originalImageInfo = document.getElementById('originalImageInfo');
  const compressedImageInfo = document.getElementById('compressedImageInfo');
  const compressionStats = document.getElementById('compressionStats');

  const HARDCODED_PROJECT_ID = 'eat-with-images';
  const FIREBASE_STORAGE_BUCKET = 'eat-with-images.firebasestorage.app';
  const FIREBASE_STORAGE_FOLDER = 'dish-images';

  testAllThingsBtn.addEventListener('click', async function() {
    console.log('🚀 Starting "Test All The Things"...');
    showStatus('🚀 Starting "Test All The Things"...', 'info');
    let allTestsPassed = true;
    let testLog = "📝 Test Log:\n";

    // Test Connection (was step 2, now step 1)
    testLog += "\n--- Test: Connection ---\n";
    console.log('🧪 Test: Connection');
    showStatus('Testing connection...', 'info');
    try {
      await new Promise((resolve, reject) => {
        chrome.identity.getAuthToken({ interactive: true }, function(token) {
          if (chrome.runtime.lastError) {
            testLog += `❌ Auth Token Failed: ${chrome.runtime.lastError.message}\n`;
            console.error('Auth Token Failed:', chrome.runtime.lastError.message);
            reject(new Error('Failed to get auth token: ' + chrome.runtime.lastError.message));
            return;
          }
          testLog += '🔑 Auth token obtained.\n';
          console.log('Auth token obtained.');

          // Test Firebase Storage connection instead of Drive
          fetch(`https://storage.googleapis.com/storage/v1/b/${FIREBASE_STORAGE_BUCKET}/o?prefix=${encodeURIComponent(FIREBASE_STORAGE_FOLDER + '/')}&maxResults=1`, {
            headers: { 'Authorization': 'Bearer ' + token }
          })
          .then(response => {
            testLog += `📞 Firebase Storage API Response Status: ${response.status}\n`;
            console.log('Firebase Storage API Response Status:', response.status);
            if (!response.ok) {
              reject(new Error(`Firebase Storage test failed: ${response.status} ${response.statusText}`));
              return response.text(); // Get text for more error details
            }
            return response.json();
          })
          .then(storageData => {
            if (typeof storageData === 'string') { // Means !response.ok and we got text
                testLog += `❌ Firebase Storage API Error: ${storageData}\n`;
                console.error('Firebase Storage API Error:', storageData);
                reject(new Error(`Firebase Storage test failed with message: ${storageData}`));
                return;
            }
            testLog += `📄 Firebase Storage Data: ${JSON.stringify(storageData).substring(0, 200)}...\n`;
            console.log('Firebase Storage Data:', storageData);
            testLog += `✅ Connection successful! Connected to Firebase Storage bucket: ${FIREBASE_STORAGE_BUCKET}\n`;
            console.log(`Connection successful! Connected to Firebase Storage bucket: ${FIREBASE_STORAGE_BUCKET}`);
            resolve();
          })
          .catch(error => {
            testLog += `❌ Connection Fetch/Parse Error: ${error.message}\n`;
            console.error('Connection Fetch/Parse Error:', error.message);
            reject(error);
          });
        });
      });
      showStatus('Connection test successful!', 'success');
    } catch (error) {
      allTestsPassed = false;
      testLog += `❌ Connection Test Failed: ${error.message}\n`;
      console.error('Connection Test Failed:', error.message);
      showStatus(`Connection test failed: ${error.message}`, 'error');
    }

    // Search Firebase Storage for Images (was step 3, now step 2)
    testLog += "\n--- Test: Search Firebase Storage ---\n";
    console.log('🧪 Test: Search Firebase Storage');
    const hardcodedSearchDishName = "Pizza"; // Hardcoded dish name for the test
    testLog += `ℹ️ Using hardcoded dish name for search: "${hardcodedSearchDishName}"\n`;
    showStatus(`Searching Firebase Storage for "${hardcodedSearchDishName}"...`, 'info');
    try {
      await new Promise((resolve, reject) => {
        chrome.runtime.sendMessage({ action: 'searchImages', dishName: hardcodedSearchDishName }, function(response) {
          testLog += `🔍 Search Response: ${JSON.stringify(response)}\n`;
          console.log('Search Response:', response);
          if (response && response.success) {
            if (response.files && response.files.length > 0) {
              testLog += `✅ Found ${response.files.length} images for "${hardcodedSearchDishName}".\n`;
              console.log(`Found ${response.files.length} images for "${hardcodedSearchDishName}".`);
            } else {
              testLog += `ℹ️ No images found for "${hardcodedSearchDishName}".\n`;
              console.log(`No images found for "${hardcodedSearchDishName}".`);
            }
            showStatus(`Search for "${hardcodedSearchDishName}" complete. Found ${response.files?.length || 0} images.`, 'success');
            resolve();
          } else {
            reject(new Error(response?.error || 'Unknown search error'));
          }
        });
      });
    } catch (error) {
      allTestsPassed = false;
      testLog += `❌ Search Failed: ${error.message}\n`;
      console.error('Search Failed:', error.message);
      showStatus(`Search failed: ${error.message}`, 'error');
    }

    // Test Image Generation (was step 4, now step 3)
    testLog += "\n--- Test: Image Generation ---\n";
    console.log('🧪 Test: Image Generation');
    showStatus('Testing image generation...', 'info');
    testImageResult.style.display = 'none';
    const testDishName = 'Rainbow Cupcake';
    const testIngredients = 'flour, sugar, eggs, butter, food coloring, sprinkles';
    try {
      await new Promise((resolve, reject) => {
        chrome.runtime.sendMessage({
          action: 'generateImage',
          dishName: testDishName,
          ingredients: testIngredients,
          model: 'imagen-3.0-generate-002'
        }, function(response) {
          testLog += `🎨 Image Generation Response: ${JSON.stringify(response).substring(0, 200)}...\n`; // Log snippet
          console.log('Image Generation Response:', response);
          if (response && response.success) {
            testImageResult.style.display = 'block';
            testImageStatus.textContent = `Successfully generated image for "${testDishName}" (Source: ${response.source})`;
            testImageStatus.className = 'status success';
            testImage.src = `data:${response.mimeType};base64,${response.imageData}`;
            testLog += `✅ Image generated successfully for "${testDishName}".\n`;
            console.log(`Image generated successfully for "${testDishName}".`);
            showStatus('Image generation successful!', 'success');
            resolve();
          } else {
            reject(new Error(response?.error || 'Unknown image generation error'));
          }
        });
      });
    } catch (error) {
      allTestsPassed = false;
      testImageResult.style.display = 'block';
      testImageStatus.textContent = `Failed to generate image: ${error.message}`;
      testImageStatus.className = 'status error';
      testImage.src = '';
      testLog += `❌ Image Generation Failed: ${error.message}\n`;
      console.error('Image Generation Failed:', error.message);
      showStatus(`Image generation failed: ${error.message}`, 'error');
    }

    // Firebase Storage test is now integrated into the connection test above

    // Test Image Compression (was step 5, now step 5)
    testLog += "\n--- Test: Image Compression ---\n";
    console.log('🧪 Test: Image Compression');
    showStatus('Testing image compression...', 'info');
    testCompressionResult.style.display = 'none';
    try {
      const testImageData = await createTestImage();
      testLog += `🖼️ Created test image for compression: ${Math.round(testImageData.length / 1024)}KB\n`;
      console.log(`Created test image for compression: ${Math.round(testImageData.length / 1024)}KB`);
      await testImageCompression(testImageData, testLog); // Pass testLog by reference (it's an object) or handle return
      showStatus('Image compression test complete.', 'success'); // Status updated within testImageCompression
    } catch (error) {
      allTestsPassed = false;
      testLog += `❌ Image Compression Setup Failed: ${error.message}\n`;
      console.error('Image Compression Setup Failed:', error.message);
      showStatus(`Image compression setup failed: ${error.message}`, 'error');
    }

    testLog += "\n--- Test Run Summary ---\n";
    if (allTestsPassed) {
      testLog += "✅ All tests passed (or were skipped by user)!\n";
      console.log("✅ All tests passed (or were skipped by user)!");
      showStatus('🎉 All tests completed successfully!', 'success');
    } else {
      testLog += "❌ Some tests failed. See logs above for details.\n";
      console.log("❌ Some tests failed. See logs above for details.");
      showStatus('⚠️ Some tests failed. Check console for details.', 'error');
    }
    console.log("📝 Full Test Log:\n", testLog);
  });


  // Create a test image for compression testing (modified to be async)
  function createTestImage() {
    return new Promise((resolve, reject) => {
      try {
        // Create a canvas with a detailed test image
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // Make it large to test compression
        canvas.width = 1024;
        canvas.height = 1024;

        // Create a detailed gradient pattern
        const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
        gradient.addColorStop(0, '#ff0000');
        gradient.addColorStop(0.2, '#ff8800');
        gradient.addColorStop(0.4, '#ffff00');
        gradient.addColorStop(0.6, '#00ff00');
        gradient.addColorStop(0.8, '#0088ff');
        gradient.addColorStop(1, '#8800ff');

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Add some text and shapes for complexity
        ctx.fillStyle = 'white';
        ctx.font = '48px Arial';
        ctx.fillText('COMPRESSION TEST', 50, 100);

        // Add circles for detail
        for (let i = 0; i < 20; i++) {
          ctx.beginPath();
          ctx.arc(
            Math.random() * canvas.width,
            Math.random() * canvas.height,
            Math.random() * 50 + 10,
            0,
            2 * Math.PI
          );
          ctx.fillStyle = `rgba(${Math.random() * 255}, ${Math.random() * 255}, ${Math.random() * 255}, 0.7)`;
          ctx.fill();
        }

        // Convert to base64
        const dataUrl = canvas.toDataURL('image/png', 1.0);
        const base64Data = dataUrl.split(',')[1];

        console.log(`Created test image: ${Math.round(base64Data.length / 1024)}KB`);
        resolve(base64Data);
      } catch (error) {
        reject(error);
      }
    });
  }

  // Test the compression function
  function testImageCompression(originalBase64) {
    console.log('🧪 Starting compression test...');
    console.log(`📊 Original image size: ${Math.round(originalBase64.length / 1024)}KB`);

    // Test compression directly in the options page context (has DOM APIs)
    if (typeof compressImageWithCanvas === 'function') {
      console.log('✅ compressImageWithCanvas function found, testing directly...');

      compressImageWithCanvas(originalBase64, 100)
        .then(result => {
          console.log('🔬 Direct compression test result:', result);

          if (result.success) {
            const response = {
              success: true,
              compressedData: result.data,
              compressedMimeType: result.mimeType,
              compressedSize: result.compressedSize,
              quality: result.quality,
              compressionWorked: true
            };
            displayCompressionResults(originalBase64, response);
          } else {
            console.error('❌ Direct compression failed:', result);
            testCompressionStatus.textContent = `Direct compression failed: ${result.error || 'Unknown error'}`;
            testCompressionStatus.className = 'status error';
            testCompressionResult.style.display = 'block';
            statusDiv.style.display = 'none';
          }
        })
        .catch(error => {
          console.error('❌ Direct compression error:', error);
          testCompressionStatus.textContent = `Direct compression error: ${error.message}`;
          testCompressionStatus.className = 'status error';
          testCompressionResult.style.display = 'block';
          statusDiv.style.display = 'none';
        });
    } else {
      console.log('❌ compressImageWithCanvas function not found, falling back to background script...');

      // Fallback: Call the background script to test compression
      chrome.runtime.sendMessage({
        action: 'testCompression',
        imageData: originalBase64,
        targetSizeKB: 100
      }, function(response) {
        console.log('📨 Background script response:', response);

        if (response && response.success) {
          displayCompressionResults(originalBase64, response);
        } else {
          testCompressionStatus.textContent = `Compression test failed: ${response?.error || 'Unknown error'}`;
          testCompressionStatus.className = 'status error';
          testCompressionResult.style.display = 'block';
          statusDiv.style.display = 'none';
        }
      });
    }
  }

  // Display compression test results
  function displayCompressionResults(originalBase64, compressionResult) {
    const originalSizeKB = Math.round(originalBase64.length / 1024);
    const compressedSizeKB = compressionResult.compressedSize || Math.round(compressionResult.compressedData.length / 1024);
    const compressionRatio = Math.round((1 - compressedSizeKB / originalSizeKB) * 100);

    // Show the test result
    testCompressionResult.style.display = 'block';
    testCompressionStatus.textContent = `Compression test completed successfully!`;
    testCompressionStatus.className = 'status success';

    // Display original image
    originalImage.src = `data:image/png;base64,${originalBase64}`;
    originalImageInfo.textContent = `${originalSizeKB}KB (PNG)`;

    // Display compressed image
    compressedImage.src = `data:${compressionResult.compressedMimeType || 'image/jpeg'};base64,${compressionResult.compressedData}`;
    compressedImageInfo.textContent = `${compressedSizeKB}KB (${compressionResult.compressedMimeType || 'JPEG'})`;

    // Display compression statistics
    compressionStats.innerHTML = `
      <h4>Compression Statistics</h4>
      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
        <div>
          <strong>Original Size:</strong> ${originalSizeKB}KB<br>
          <strong>Compressed Size:</strong> ${compressedSizeKB}KB<br>
          <strong>Size Reduction:</strong> ${compressionRatio}%<br>
          <strong>Target Size:</strong> 100KB
        </div>
        <div>
          <strong>Original Format:</strong> PNG<br>
          <strong>Compressed Format:</strong> ${compressionResult.compressedMimeType || 'JPEG'}<br>
          <strong>Quality Used:</strong> ${compressionResult.quality || 'Unknown'}<br>
          <strong>Target Met:</strong> ${compressedSizeKB <= 100 ? '✅ Yes' : '❌ No'}
        </div>
      </div>
      <div style="margin-top: 15px;">
        <strong>Compression Function Status:</strong>
        ${compressionResult.compressionWorked ? '✅ Working correctly' : '❌ Not working properly'}
      </div>
    `;

    // Hide the main status
    statusDiv.style.display = 'none';

    console.log('Compression test results:', {
      originalSizeKB,
      compressedSizeKB,
      compressionRatio,
      targetMet: compressedSizeKB <= 100,
      compressionWorked: compressionResult.compressionWorked
    });
  }

  function showStatus(message, type) {
    statusDiv.textContent = message;
    statusDiv.className = 'status ' + type;
    statusDiv.style.display = 'block';

    // Hide after 5 seconds if it's a success message
    if (type === 'success') {
      setTimeout(function() {
        statusDiv.style.display = 'none';
      }, 5000);
    }
  }
});
