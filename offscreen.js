// offscreen.js - <PERSON>les image compression in the offscreen document

if (typeof compressImageWithCanvas !== 'function') {
  console.error('CRITICAL: compressImageWithCanvas function not found in offscreen.js. Ensure compression.js is loaded before offscreen.js.');
}

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'compressImageOffscreen') {
    console.log('[Offscreen] Received compression request:', request);
    if (!request.imageData || !request.targetSizeKB) {
      console.error('[Offscreen] Invalid request data for compression.');
      sendResponse({ success: false, error: 'Invalid request data for offscreen compression.' });
      return true; // Keep message channel open for async response
    }

    if (typeof compressImageWithCanvas === 'function') {
      compressImageWithCanvas(request.imageData, request.targetSizeKB)
        .then(compressionResult => {
          console.log('[Offscreen] Compression result:', compressionResult);
          sendResponse(compressionResult);
        })
        .catch(error => {
          console.error('[Offscreen] Error during compression:', error);
          sendResponse({
            success: false,
            error: 'Offscreen compression failed',
            details: error.message,
            fallbackData: request.imageData, // Send original back on error
            fallbackMimeType: 'image/png' // Assuming original might be PNG
          });
        });
    } else {
      console.error('[Offscreen] compressImageWithCanvas is not defined. Cannot compress.');
      sendResponse({
        success: false,
        error: 'compressImageWithCanvas not available in offscreen document.',
        fallbackData: request.imageData,
        fallbackMimeType: 'image/png'
      });
    }
    return true; // Indicate that the response will be sent asynchronously
  }
});

console.log('[Offscreen] Offscreen document script loaded and listener attached.');
