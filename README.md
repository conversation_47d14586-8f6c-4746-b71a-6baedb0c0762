# Eat (with images) Bookmarklet

This bookmarklet generates images for dishes on a webpage using the Gemini 2.5 Flash API.

## How to Use

1. Open the `bookmarklet.html` file in your browser
2. Drag the "Eat (with images)" link to your bookmarks bar
3. Navigate to a food menu website
4. Click the bookmarklet to generate images for dishes on the page

## How It Works

The bookmarklet:
1. Finds all elements with class `.dishItemComponent` on the page
2. Extracts dish names from `.dish-header-button` elements
3. Extracts ingredients from `.ingredients-list` elements
4. Generates an image for each dish using the Gemini 2.5 Flash API
5. Inserts the generated image into the `.dish-details` element

## Files

- `bookmarklet.js` - The unminified, readable version of the bookmarklet code
- `bookmarklet.min.js` - The minified version used as the actual bookmarklet
- `bookmarklet.html` - A test page with instructions and sample dish elements

## Customization

You can modify the following variables in `bookmarklet.js`:

- `PROJECT_ID` - Your Google Cloud project ID
- `LOCATION_ID` - The location of your API (e.g., "global")
- `API_ENDPOINT` - The API endpoint (e.g., "aiplatform.googleapis.com")
- `MODEL_ID` - The Gemini model ID (e.g., "gemini-2.5-flash-preview-05-20")
- `BASE_PROMPT` - The prompt template used to generate images

You can also customize the generation parameters:
- `temperature` - Controls randomness (0.0 to 1.0)
- `maxOutputTokens` - Maximum number of tokens to generate
- `topP` - Controls diversity via nucleus sampling
- `seed` - Random seed for reproducibility

After making changes to `bookmarklet.js`, you'll need to minify it again to update the bookmarklet.

## Requirements

- The webpage must have elements with the following structure:
  ```html
  <div class="dishItemComponent">
      <div class="dish-header-button">Dish Name</div>
      <div class="ingredients-list">ingredient1, ingredient2, ingredient3, ...</div>
      <div class="dish-details">
          <!-- Generated image will be inserted here -->
          <div class="allergens">...</div>
      </div>
  </div>
  ```
- Dishes must have at least 4 ingredients to generate an image

## Browser Compatibility

This bookmarklet uses modern JavaScript features and requires a browser that supports:
- Fetch API
- Async/Await
- ReadableStream
- TextDecoder

It has been tested on the latest versions of Chrome, Firefox, and Edge.
