// Content script for Dish Image Generator Chrome Extension
// This script handles DOM parsing and image insertion on web pages


// Processing state
let isProcessingStopped = false;
let autoLoadEnabled = true; // Default to enabled

// Content script level deduplication to prevent multiple simultaneous requests
const activeContentRequests = new Map();

// Helper function to send deduplicated requests to background script
async function sendDeduplicatedBackgroundRequest(dishName, ingredients, model) {
  const requestKey = dishName.toLowerCase().trim();

  // Check if there's already an active request for this dish
  if (activeContentRequests.has(requestKey)) {
    console.log(`⏳ Content script: Request for "${dishName}" already in progress, waiting for completion...`);
    return await activeContentRequests.get(requestKey);
  }

  // Create a promise for this request and store it
  const requestPromise = new Promise((resolve, reject) => {
    chrome.runtime.sendMessage({
      action: 'generateImage',
      dishName: dishName,
      ingredients: ingredients,
      model: model
    }, (response) => {
      if (chrome.runtime.lastError) {
        console.error(`Chrome runtime error for ${dishName}:`, chrome.runtime.lastError.message);
        reject(new Error(chrome.runtime.lastError.message));
      } else {
        console.log(`📥 Received response for ${dishName}:`, response);
        resolve(response);
      }
    });
  });

  activeContentRequests.set(requestKey, requestPromise);

  try {
    const result = await requestPromise;
    return result;
  } finally {
    // Clean up the active request
    activeContentRequests.delete(requestKey);
  }
}

// Configuration
const BASE_PROMPT = "professional photo of {dish_name} made with {ingredients} on a table in a restaurant. Natural lighting, close-up shot from a slightly high angle. shallow dof. no distractions";

// DOM parsing functions extracted from bookmarklet_working.js
function findDishElements() {
  const dishElements = document.querySelectorAll('.dishItemComponent');
  console.log(`Found ${dishElements.length} dish elements on the page`);
  return dishElements;
}

function extractDishInfo(dishItemComponent) {
  const dishNameElement = dishItemComponent.querySelector('.dish-header-button');
  const ingredientsElement = dishItemComponent.querySelector('.ingredients-list');

  const dishName = dishNameElement ? dishNameElement.textContent.trim() : '';
  const ingredients = ingredientsElement ? ingredientsElement.textContent.trim() : '';

  return { dishName, ingredients };
}

function insertImageIntoDish(dishItemComponent, imageData, mimeType) {
  // Remove any existing generated image (check for both image and wrapper)
  const existingImage = dishItemComponent.querySelector('.generated-dish-image');
  if (existingImage) {
    // Remove the wrapper div if it exists, otherwise remove the image directly
    const wrapper = existingImage.parentElement;
    if (wrapper && wrapper.tagName === 'DIV' && wrapper.children.length === 1) {
      wrapper.remove();
    } else {
      existingImage.remove();
    }
  }

  // Create new image element
  const img = document.createElement('img');
  img.src = `data:${mimeType};base64,${imageData}`;
  img.classList.add('generated-dish-image');
  img.style.maxWidth = '100%';
  img.style.height = 'auto';
  img.style.borderRadius = '4px';
  img.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';

  // Create wrapper div with margin
  const imageWrapper = document.createElement('div');
  imageWrapper.style.marginTop = '10px';
  imageWrapper.style.marginRight = '13px';
  imageWrapper.appendChild(img);

  // Find insertion point - insert as sibling after venue-dish-header
  const venueHeader = dishItemComponent.querySelector('.venue-dish-header');

  if (venueHeader) {
    // Insert image wrapper as a sibling after the venue-dish-header element
    venueHeader.insertAdjacentElement('afterend', imageWrapper);
    console.log('Image inserted successfully after venue-dish-header');
  } else {
    // Fallback to dish-details if venue-dish-header not found
    const dishDetails = dishItemComponent.querySelector('.dish-details');
    const allergensElement = dishDetails ? dishDetails.querySelector('.allergens') : null;

    if (dishDetails) {
      if (allergensElement) {
        dishDetails.insertBefore(imageWrapper, allergensElement);
      } else {
        dishDetails.appendChild(imageWrapper);
      }
      console.log('Image inserted successfully into dish details (fallback)');
    } else {
      console.error('Neither venue-dish-header nor dish-details element found for image insertion');
    }
  }
}

function showLoadingIndicator(dishItemComponent, dishName) {
  // Don't show loading indicator if an image is already there
  const existingImage = dishItemComponent.querySelector('.generated-dish-image');
  if (existingImage) {
    console.log(`Skipping loading indicator for ${dishName} - image already present`);
    return null;
  }

  // Remove any existing loading indicator
  const existingLoader = dishItemComponent.querySelector('.dish-image-loading');
  if (existingLoader) {
    existingLoader.remove();
  }

  // Create loading indicator
  const loader = document.createElement('div');
  loader.classList.add('dish-image-loading');
  loader.style.cssText = `
    padding: 10px;
    text-align: center;
    background-color: #f5f5f5;
    border-radius: 4px;
    margin-top: 10px;
    font-size: 14px;
    color: #666;
  `;
  loader.innerHTML = `
    <div style="display: inline-block; width: 20px; height: 20px; border: 2px solid #ddd; border-top: 2px solid #4285f4; border-radius: 50%; animation: spin 1s linear infinite;"></div>
  `;

  // Add CSS animation for spinner
  if (!document.getElementById('dish-spinner-style')) {
    const style = document.createElement('style');
    style.id = 'dish-spinner-style';
    style.textContent = `
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `;
    document.head.appendChild(style);
  }

  // Insert loading indicator as sibling after venue-dish-header, or inside dish-details as fallback
  const venueHeader = dishItemComponent.querySelector('.venue-dish-header');
  const dishDetails = dishItemComponent.querySelector('.dish-details');

  if (venueHeader) {
    venueHeader.insertAdjacentElement('afterend', loader);
  } else if (dishDetails) {
    dishDetails.appendChild(loader);
  }

  return loader;
}

function removeLoadingIndicator(dishItemComponent) {
  const loader = dishItemComponent.querySelector('.dish-image-loading');
  if (loader) {
    loader.remove();
  }
}

function showErrorIndicator(dishItemComponent, dishName, error) {
  removeLoadingIndicator(dishItemComponent);

  const errorDiv = document.createElement('div');
  errorDiv.classList.add('dish-image-error');
  errorDiv.style.cssText = `
    padding: 10px;
    background-color: #ffebee;
    border: 1px solid #f44336;
    border-radius: 4px;
    margin-top: 10px;
    font-size: 14px;
    color: #d32f2f;
  `;
  errorDiv.textContent = `Failed to generate image for ${dishName}: ${error}`;

  // Insert error indicator as sibling after venue-dish-header, or inside dish-details as fallback
  const venueHeader = dishItemComponent.querySelector('.venue-dish-header');
  const dishDetails = dishItemComponent.querySelector('.dish-details');

  if (venueHeader) {
    venueHeader.insertAdjacentElement('afterend', errorDiv);
  } else if (dishDetails) {
    dishDetails.appendChild(errorDiv);
  }
}

function countIngredients(ingredientsString) {
  if (!ingredientsString || typeof ingredientsString !== 'string') {
    return 0;
  }

  // Split by commas and filter out empty/whitespace-only items
  const ingredientList = ingredientsString
    .split(',')
    .map(ingredient => ingredient.trim())
    .filter(ingredient => ingredient.length > 0);

  return ingredientList.length;
}

function getSourceDescription(source) {
  switch (source) {
    case 'cache':
      return '📦 Retrieved from local cache (Chrome storage → Google Drive)';
    case 'drive':
      return '☁️ Found existing image in Google Drive';
    case 'generated':
      return '🎨 Generated new image using AI (Imagen API)';
    default:
      return `❓ Unknown source: ${source}`;
  }
}

async function processIndividualDish(dishItemComponent) {
  await processIndividualDishWithResult(dishItemComponent);
}

async function processIndividualDishWithResult(dishItemComponent) {
  const { dishName, ingredients } = extractDishInfo(dishItemComponent);

  if (!dishName) {
    console.error('Dish name not found for a dish item');
    return { success: false, error: 'No dish name found' };
  }

  console.log(`Processing dish: ${dishName}`);

  // Show loading indicator
  showLoadingIndicator(dishItemComponent, dishName);

  try {
    console.log(`🔍 Looking for image for: ${dishName} (checking cache → Drive → generate if needed)`);

    // Send deduplicated message to background script to get or generate image
    const response = await sendDeduplicatedBackgroundRequest(dishName, ingredients, 'imagen-3.0-generate-002');

    // Remove loading indicator
    removeLoadingIndicator(dishItemComponent);

    if (response.success) {
      // Insert the image into the DOM
      const sourceDescription = getSourceDescription(response.source);
      console.log(`✅ ${dishName}: ${sourceDescription}`);
      insertImageIntoDish(dishItemComponent, response.imageData, response.mimeType);
      console.log(`🖼️ Image inserted successfully for ${dishName} (${response.source})`);
      return { success: true, source: response.source, dishName: dishName };
    } else {
      console.error(`❌ Failed to process ${dishName}:`, response.error);
      showErrorIndicator(dishItemComponent, dishName, response.error);
      return { success: false, error: response.error, dishName: dishName };
    }
  } catch (error) {
    console.error(`Error processing ${dishName}:`, error);
    removeLoadingIndicator(dishItemComponent);
    showErrorIndicator(dishItemComponent, dishName, error.message);
    return { success: false, error: error.message, dishName: dishName };
  }
}

async function processAllDishesOnPage() {
  // Reset stop flag
  isProcessingStopped = false;

  const dishElements = findDishElements();

  if (dishElements.length === 0) {
    console.log('No dish elements found on this page');
    return { success: false, message: 'No dish elements found on this page' };
  }

  console.log(`🚀 Starting two-phase loading strategy for ${dishElements.length} dishes`);

  // PHASE 1: Instant Cache Analysis & Display
  console.log('📋 PHASE 1: Analyzing cache status for all dishes...');
  const dishAnalysis = await analyzeDishesForBatchProcessing(dishElements);

  console.log(`📊 Analysis complete: ${dishAnalysis.valid.length} valid dishes, ${dishAnalysis.skipped.length} skipped`);
  console.log(`📦 Cache status: ${dishAnalysis.cached.length} cached, ${dishAnalysis.uncached.length} uncached`);

  // Show immediate loading indicators for all valid dishes
  showInitialLoadingIndicators(dishAnalysis);

  // PHASE 2: Batch Processing by Type
  console.log('⚡ PHASE 2: Starting batch processing...');
  const results = await batchProcessDishes(dishAnalysis);

  // Compile final results with enhanced progress feedback
  const totalProcessed = results.cache + results.drive + results.generated;
  const totalSkipped = dishAnalysis.skipped.length;

  let message = `Processed ${totalProcessed} dishes`;
  if (totalSkipped > 0) {
    message += `, skipped ${totalSkipped} dishes (no dish name)`;
  }
  if (results.stopped > 0) {
    message += `, stopped before processing ${results.stopped} dishes`;
  }

  // Add source breakdown with performance metrics
  if (totalProcessed > 0) {
    const sourceBreakdown = [];
    if (results.cache > 0) sourceBreakdown.push(`${results.cache} from cache (instant)`);
    if (results.drive > 0) sourceBreakdown.push(`${results.drive} from Drive`);
    if (results.generated > 0) sourceBreakdown.push(`${results.generated} generated`);

    if (sourceBreakdown.length > 0) {
      message += ` (${sourceBreakdown.join(', ')})`;
    }
  }

  // Add retry information if any retries occurred
  if (results.retries && results.retries > 0) {
    message += `. ${results.retries} retry attempts were made due to API rate limits or transient errors`;
  }

  console.log(`📊 Optimized parallel processing complete: ${message}`);
  console.log(`📈 Performance metrics: Cache=${results.cache} (instant), Drive=${results.drive}, Generated=${results.generated}, Retries=${results.retries || 0}`);
  console.log(`⚡ Parallel processing used batches of ${PARALLEL_CONFIG.batchSize} with max ${PARALLEL_CONFIG.maxConcurrentRequests} concurrent requests`);

  return {
    success: true,
    message: message,
    metrics: {
      cache: results.cache,
      drive: results.drive,
      generated: results.generated,
      retries: results.retries || 0,
      skipped: totalSkipped,
      stopped: results.stopped || 0,
      parallelConfig: PARALLEL_CONFIG
    }
  };
}

// PHASE 1: Analyze all dishes and check cache status
async function analyzeDishesForBatchProcessing(dishElements) {
  console.log('🔍 Analyzing dishes for batch processing...');

  const analysis = {
    valid: [],      // Dishes with valid names and sufficient ingredients
    skipped: [],    // Dishes that don't meet requirements
    cached: [],     // Valid dishes with cache entries
    uncached: []    // Valid dishes without cache entries
  };

  // First pass: validate dishes and extract info
  for (let i = 0; i < dishElements.length; i++) {
    const dishElement = dishElements[i];
    const { dishName, ingredients } = extractDishInfo(dishElement);

    const dishInfo = {
      element: dishElement,
      dishName: dishName,
      ingredients: ingredients,
      index: i + 1,
      total: dishElements.length
    };

    // Validate dish name
    if (!dishName) {
      analysis.skipped.push({ ...dishInfo, reason: 'No dish name found' });
      console.log(`❌ Skipped dish ${i + 1} - no dish name found`);
      continue;
    }

    analysis.valid.push(dishInfo);
    console.log(`✅ Valid dish: "${dishName}"`);
  }

  // Second pass: check cache status for valid dishes
  console.log('📦 Checking cache status for valid dishes...');

  for (const dishInfo of analysis.valid) {
    try {
      // Check Chrome local storage cache for actual image data
      const cacheEntry = await new Promise((resolve) => {
        chrome.storage.local.get(['imageCache'], (result) => {
          const cache = result.imageCache || {};
          const cacheEntry = cache[dishInfo.dishName];

          // Debug: Show all cache keys and the dish name we're looking for
          const cacheKeys = Object.keys(cache);
          console.log(`🔍 Cache lookup for "${dishInfo.dishName}"`);
          console.log(`📋 Available cache keys (${cacheKeys.length}):`, cacheKeys);
          console.log(`🎯 Looking for exact match:`, cacheEntry ? 'Found entry' : 'No entry');
          if (cacheEntry) {
            console.log(`📦 Cache entry details:`, cacheEntry);
          }

          resolve(cacheEntry);
        });
      });

      if (cacheEntry && cacheEntry.imageData) {
        // Has actual image data - can load instantly
        dishInfo.cacheEntry = cacheEntry;
        analysis.cached.push(dishInfo);
        console.log(`📦 "${dishInfo.dishName}" - found complete cache entry with image data (${cacheEntry.imageData.length} chars)`);
      } else if (cacheEntry && cacheEntry.fileId) {
        // Has file ID but no image data - needs Drive retrieval
        dishInfo.cachedFileId = cacheEntry.fileId;
        analysis.uncached.push(dishInfo);
        console.log(`🔍 "${dishInfo.dishName}" - has file ID (${cacheEntry.fileId}) but no image data, needs Drive retrieval`);
      } else {
        // No cache entry at all - needs Drive search/generation
        analysis.uncached.push(dishInfo);
        console.log(`🔍 "${dishInfo.dishName}" - not in cache, will need Drive search/generation`);
      }
    } catch (error) {
      console.error(`Error checking cache for ${dishInfo.dishName}:`, error);
      analysis.uncached.push(dishInfo);
    }
  }

  return analysis;
}

// Show immediate loading indicators for all valid dishes
function showInitialLoadingIndicators(analysis) {
  console.log('🎯 Showing initial loading indicators...');
  console.log(`📊 Analysis breakdown: ${analysis.cached.length} cached, ${analysis.uncached.length} uncached`);

  // Show loading indicators for cached dishes
  analysis.cached.forEach(dishInfo => {
    console.log(`📦 Showing cache indicator for: ${dishInfo.dishName}`);
    showSpecificLoadingIndicator(dishInfo.element, dishInfo.dishName, 'cache');
  });

  // Show loading indicators for uncached dishes
  analysis.uncached.forEach(dishInfo => {
    console.log(`🔍 Showing search indicator for: ${dishInfo.dishName}`);
    showSpecificLoadingIndicator(dishInfo.element, dishInfo.dishName, 'search');
  });

  console.log(`💫 Displayed ${analysis.cached.length + analysis.uncached.length} loading indicators`);
}

function showSpecificLoadingIndicator(dishItemComponent, dishName, type) {
  console.log(`🎯 showSpecificLoadingIndicator called for "${dishName}" with type="${type}"`);

  // Don't show loading indicator if an image is already there
  const existingImage = dishItemComponent.querySelector('.generated-dish-image');
  if (existingImage) {
    console.log(`Skipping loading indicator for ${dishName} - image already present`);
    return;
  }

  // Remove any existing loading indicator
  const existingLoader = dishItemComponent.querySelector('.dish-image-loading');
  if (existingLoader) {
    existingLoader.remove();
  }

  // Create specific loading indicator
  const loader = document.createElement('div');
  loader.classList.add('dish-image-loading');
  loader.setAttribute('data-loading-type', type);

  const color = type === 'cache' ? '#4285f4' : type === 'search' ? '#ff9800' : '#9c27b0';
  const bgColor = type === 'cache' ? '#e3f2fd' : type === 'search' ? '#fff3e0' : '#f3e5f5';

  loader.style.cssText = `
    padding: 10px;
    margin-top: 10px;
    margin-right: 13px; /* Added to match image margin */
    border-radius: 4px;
    /* Removed background-color and border */
    background-color: transparent;
    border: none;
    /* Use flexbox for centering the spinner */
    display: flex;
    justify-content: center;
    align-items: center;
  `;

  loader.innerHTML = `
    <div style="width: 20px; height: 20px; border: 2px solid #ddd; border-top: 2px solid ${color}; border-radius: 50%; animation: spin 1s linear infinite;"></div>
  `;

  // Add CSS animation for spinner if not already added
  if (!document.getElementById('dish-spinner-style')) {
    const style = document.createElement('style');
    style.id = 'dish-spinner-style';
    style.textContent = `
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `;
    document.head.appendChild(style);
  }

  // Insert loading indicator
  const venueHeader = dishItemComponent.querySelector('.venue-dish-header');
  const dishDetails = dishItemComponent.querySelector('.dish-details');

  if (venueHeader) {
    venueHeader.insertAdjacentElement('afterend', loader);
  } else if (dishDetails) {
    dishDetails.appendChild(loader);
  }
}

// Parallel processing configuration
const PARALLEL_CONFIG = {
  maxConcurrentRequests: 4,    // Maximum concurrent requests
  batchSize: 3,                // Process in smaller batches
  delayBetweenBatches: 500,    // 500ms delay between batches
  generationDelay: 1000        // 1 second delay only after generation
};

// PHASE 2: Batch process dishes by type with parallel processing
async function batchProcessDishes(analysis) {
  console.log('⚡ Starting optimized batch processing with parallel execution...');

  const results = {
    cache: 0,
    drive: 0,
    generated: 0,
    stopped: 0,
    retries: 0
  };

  // Batch 1: Process cached dishes (instant - all at once!)
  if (analysis.cached.length > 0) {
    console.log(`📦 Batch 1: Processing ${analysis.cached.length} cached dishes instantly...`);

    // Process all cached dishes simultaneously (no await needed - they're instant!)
    const cachePromises = analysis.cached.map(async (dishInfo) => {
      if (isProcessingStopped) return null;

      updateLoadingIndicator(dishInfo.element, 'cache');

      const result = await processCachedDish(dishInfo);
      if (result && result.success) {
        results.cache++;
      }
      return result;
    });

    // Wait for all cached dishes to complete (should be nearly instant)
    await Promise.all(cachePromises);

    console.log(`⚡ Batch 1 complete: ${results.cache} dishes loaded from cache INSTANTLY`);
  }

  // Batch 2: Process uncached dishes with parallel processing and intelligent batching
  if (analysis.uncached.length > 0 && !isProcessingStopped) {
    console.log(`🔍 Batch 2: Processing ${analysis.uncached.length} uncached dishes with parallel processing...`);
    console.log(`⚙️ Using ${PARALLEL_CONFIG.maxConcurrentRequests} max concurrent requests, batch size: ${PARALLEL_CONFIG.batchSize}`);

    // Split uncached dishes into smaller batches for controlled parallel processing
    const batches = [];
    for (let i = 0; i < analysis.uncached.length; i += PARALLEL_CONFIG.batchSize) {
      batches.push(analysis.uncached.slice(i, i + PARALLEL_CONFIG.batchSize));
    }

    console.log(`📊 Split ${analysis.uncached.length} dishes into ${batches.length} batches of max ${PARALLEL_CONFIG.batchSize} dishes each`);

    // Process each batch with controlled parallelism
    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      if (isProcessingStopped) {
        const remainingDishes = batches.slice(batchIndex).flat().length;
        results.stopped += remainingDishes;
        console.log(`⏹️ Processing stopped, ${remainingDishes} dishes remaining`);
        break;
      }

      const batch = batches[batchIndex];
      console.log(`🚀 Processing batch ${batchIndex + 1}/${batches.length} with ${batch.length} dishes...`);

      // Update loading indicators for the current batch
      batch.forEach(dishInfo => {
        updateLoadingIndicator(dishInfo.element, 'search');
      });

      // Process batch in parallel with controlled concurrency
      const batchResults = await processUncachedBatchParallel(batch);

      // Update results
      results.drive += batchResults.drive;
      results.generated += batchResults.generated;
      results.retries += batchResults.retries;

      console.log(`✅ Batch ${batchIndex + 1} complete: ${batchResults.drive} from Drive, ${batchResults.generated} generated, ${batchResults.retries} retries`);

      // Add delay between batches (except for the last batch)
      if (batchIndex < batches.length - 1 && !isProcessingStopped) {
        console.log(`⏱️ Waiting ${PARALLEL_CONFIG.delayBetweenBatches}ms before next batch...`);
        await new Promise(resolve => setTimeout(resolve, PARALLEL_CONFIG.delayBetweenBatches));
      }
    }

    console.log(`✅ Batch 2 complete: ${results.drive} from Drive, ${results.generated} generated, ${results.retries} total retries`);
  }

  return results;
}

// Process a batch of uncached dishes in parallel with intelligent rate limiting
async function processUncachedBatchParallel(batch) {
  const batchResults = {
    drive: 0,
    generated: 0,
    retries: 0
  };

  // Process all dishes in the batch simultaneously
  const batchPromises = batch.map(async (dishInfo, index) => {
    if (isProcessingStopped) return null;

    try {
      console.log(`🔍 Processing uncached dish in parallel: ${dishInfo.dishName} (${index + 1}/${batch.length})`);

      // Send deduplicated message to background script to search Drive or generate
      const response = await sendDeduplicatedBackgroundRequest(dishInfo.dishName, dishInfo.ingredients, 'imagen-3.0-generate-002');

      // Track retry attempts if available and show retry status
      if (response.retryAttempts && response.retryAttempts > 0) {
        batchResults.retries += response.retryAttempts;
        console.log(`🔄 ${dishInfo.dishName} required ${response.retryAttempts} retries`);

        // Briefly show retry status in UI
        updateLoadingIndicator(dishInfo.element, 'retry', `Completed after ${response.retryAttempts} retries`);
        await new Promise(resolve => setTimeout(resolve, 1000)); // Show retry status for 1 second
      }

      // Update loading indicator based on result
      if (response.success && response.source === 'generated') {
        updateLoadingIndicator(dishInfo.element, 'generate');
        // Small delay to show the generation indicator
        await new Promise(resolve => setTimeout(resolve, 300));
      }

      // Remove loading indicator
      removeLoadingIndicator(dishInfo.element);

      if (response.success) {
        const sourceDescription = getSourceDescription(response.source);
        console.log(`✅ ${dishInfo.dishName}: ${sourceDescription}`);

        // Update cache with image data for future instant loading (only for Drive retrievals)
        // Generated images are already cached by the background script with pre-compressed data
        if (response.source === 'drive' && response.imageData) {
          console.log(`💾 Updating cache for ${dishInfo.dishName} with image data for instant future loading`);
          await updateCacheWithImageData(dishInfo.dishName, response.imageData, response.mimeType, response.fileId);
        } else if (response.source === 'generated') {
          console.log(`🎨 Generated image for ${dishInfo.dishName} - cache already updated by background script with pre-compressed data`);
        }

        insertImageIntoDish(dishInfo.element, response.imageData, response.mimeType);

        // Track results by source
        if (response.source === 'drive') {
          batchResults.drive++;
        } else if (response.source === 'generated') {
          batchResults.generated++;
        }

        return { success: true, source: response.source, dishName: dishInfo.dishName };
      } else {
        console.error(`❌ Failed to process ${dishInfo.dishName}:`, response.error);
        showErrorIndicator(dishInfo.element, dishInfo.dishName, response.error);
        return { success: false, error: response.error, dishName: dishInfo.dishName };
      }
    } catch (error) {
      console.error(`Error processing ${dishInfo.dishName}:`, error);
      removeLoadingIndicator(dishInfo.element);
      showErrorIndicator(dishInfo.element, dishInfo.dishName, error.message);
      return { success: false, error: error.message, dishName: dishInfo.dishName };
    }
  });

  // Wait for all dishes in the batch to complete
  const batchResultsArray = await Promise.all(batchPromises);

  // Filter out null results (from stopped processing)
  const validResults = batchResultsArray.filter(result => result !== null);

  // Add intelligent delay after generation requests
  const generatedCount = validResults.filter(result => result && result.success && result.source === 'generated').length;
  if (generatedCount > 0) {
    console.log(`⏱️ Batch contained ${generatedCount} generated images, adding ${PARALLEL_CONFIG.generationDelay}ms delay for rate limiting...`);
    await new Promise(resolve => setTimeout(resolve, PARALLEL_CONFIG.generationDelay));
  }

  console.log(`📊 Batch parallel processing complete: ${batchResults.drive} from Drive, ${batchResults.generated} generated, ${batchResults.retries} retries`);
  return batchResults;
}

function updateLoadingIndicator(dishElement, type, statusText = '') {
  const loader = dishElement.querySelector('.dish-image-loading');
  if (loader) {
    const color = type === 'cache' ? '#4285f4' :
                  type === 'search' ? '#ff9800' :
                  type === 'generate' ? '#9c27b0' :
                  type === 'retry' ? '#f44336' : '#666';
    const bgColor = type === 'cache' ? '#e3f2fd' :
                    type === 'search' ? '#fff3e0' :
                    type === 'generate' ? '#f3e5f5' :
                    type === 'retry' ? '#ffebee' : '#f5f5f5';

    // Update the colors
    loader.style.color = color;
    loader.style.borderColor = color;
    loader.style.backgroundColor = bgColor;

    // Create status text based on type
    let displayText = '';
    switch (type) {
      case 'cache':
        displayText = 'Loading from cache...';
        break;
      case 'search':
        displayText = 'Searching Drive...';
        break;
      case 'generate':
        displayText = 'Generating image...';
        break;
      case 'retry':
        displayText = statusText || 'Retrying...';
        break;
      default:
        displayText = statusText || 'Processing...';
    }

    // Update the entire innerHTML with enhanced status (removed text)
    loader.innerHTML = `
      <div style="width: 16px; height: 16px; border: 2px solid #ddd; border-top: 2px solid ${color}; border-radius: 50%; animation: spin 1s linear infinite;"></div>
    `;
  }
}

async function processCachedDish(dishInfo) {
  try {
    console.log(`📦 Processing cached dish: ${dishInfo.dishName} (direct cache retrieval)`);

    // Directly retrieve from Chrome local storage cache (much faster!)
    const cacheData = await new Promise((resolve) => {
      chrome.storage.local.get(['imageCache'], (result) => {
        const cache = result.imageCache || {};
        const cacheEntry = cache[dishInfo.dishName];
        resolve(cacheEntry);
      });
    });

    if (cacheData && cacheData.imageData) {
      // Remove loading indicator
      removeLoadingIndicator(dishInfo.element);

      // Directly insert the cached image (instant!)
      console.log(`⚡ ${dishInfo.dishName}: 📦 Retrieved from local cache (Chrome storage → Google Drive) - INSTANT`);
      insertImageIntoDish(dishInfo.element, cacheData.imageData, cacheData.mimeType || 'image/png');
      return { success: true, source: 'cache' };
    } else {
      // Cache entry exists but no image data - fall back to background script
      console.log(`⚠️ ${dishInfo.dishName}: Cache entry found but no image data, falling back to Drive retrieval`);

      const response = await sendDeduplicatedBackgroundRequest(dishInfo.dishName, dishInfo.ingredients, 'imagen-3.0-generate-002');

      // Remove loading indicator
      removeLoadingIndicator(dishInfo.element);

      if (response.success) {
        const sourceDescription = getSourceDescription(response.source);
        console.log(`✅ ${dishInfo.dishName}: ${sourceDescription}`);

        // Update cache with image data for future instant loading (only for Drive retrievals)
        // Generated images are already cached by the background script with pre-compressed data
        if (response.source === 'drive' && response.imageData) {
          console.log(`💾 Updating cache for ${dishInfo.dishName} with image data for instant future loading`);
          await updateCacheWithImageData(dishInfo.dishName, response.imageData, response.mimeType, response.fileId);
        } else if (response.source === 'generated') {
          console.log(`🎨 Generated image for ${dishInfo.dishName} - cache already updated by background script with pre-compressed data`);
        }

        insertImageIntoDish(dishInfo.element, response.imageData, response.mimeType);
        return { success: true, source: response.source };
      } else {
        showErrorIndicator(dishInfo.element, dishInfo.dishName, response.error);
        return { success: false, error: response.error };
      }
    }
  } catch (error) {
    removeLoadingIndicator(dishInfo.element);
    showErrorIndicator(dishInfo.element, dishInfo.dishName, error.message);
    return { success: false, error: error.message };
  }
}

async function processUncachedDish(dishInfo) {
  try {
    console.log(`🔍 Processing uncached dish: ${dishInfo.dishName}`);

    // Send deduplicated message to background script to search Drive or generate
    const response = await sendDeduplicatedBackgroundRequest(dishInfo.dishName, dishInfo.ingredients, 'imagen-3.0-generate-002');

    // Update loading indicator based on result
    if (response.success && response.source === 'generated') {
      updateLoadingIndicator(dishInfo.element, 'generate');
      // Small delay to show the generation indicator
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // Remove loading indicator
    removeLoadingIndicator(dishInfo.element);

    if (response.success) {
      const sourceDescription = getSourceDescription(response.source);
      console.log(`✅ ${dishInfo.dishName}: ${sourceDescription}`);

      // Update cache with image data for future instant loading (only for Drive retrievals)
      // Generated images are already cached by the background script with pre-compressed data
      if (response.source === 'drive' && response.imageData) {
        console.log(`💾 Updating cache for ${dishInfo.dishName} with image data for instant future loading`);
        await updateCacheWithImageData(dishInfo.dishName, response.imageData, response.mimeType, response.fileId);
      } else if (response.source === 'generated') {
        console.log(`🎨 Generated image for ${dishInfo.dishName} - cache already updated by background script with pre-compressed data`);
      }

      insertImageIntoDish(dishInfo.element, response.imageData, response.mimeType);
      return { success: true, source: response.source };
    } else {
      showErrorIndicator(dishInfo.element, dishInfo.dishName, response.error);
      return { success: false, error: response.error };
    }
  } catch (error) {
    removeLoadingIndicator(dishInfo.element);
    showErrorIndicator(dishInfo.element, dishInfo.dishName, error.message);
    return { success: false, error: error.message };
  }
}

// Note: Image compression logic has been moved to Code.js and is now applied
// at the point of image generation rather than during caching

// Update cache with image data for instant future loading
// Note: Image data is now pre-compressed from the generation function
async function updateCacheWithImageData(dishName, imageData, mimeType, fileId = null) {
  return new Promise((resolve) => {
    try {
      chrome.storage.local.get(['imageCache'], (result) => {
        const cache = result.imageCache || {};

        const imageSizeKB = Math.round(imageData.length / 1024);
        console.log(`💾 Attempting to cache "${dishName}" (${imageSizeKB}KB - already compressed)`);

        // Smart cache management based on storage usage
        chrome.storage.local.getBytesInUse(null, (bytesInUse) => {
          const usageMB = bytesInUse / (1024 * 1024);
          const limitMB = 9; // Leave 1MB buffer
          const newImageMB = imageSizeKB / 1024;

          // If adding this image would exceed the limit, remove old entries
          if (usageMB + newImageMB > limitMB) {
            const cacheEntries = Object.entries(cache);
            // Sort by timestamp (oldest first) and remove entries until we have space
            cacheEntries.sort((a, b) => (a[1].timestamp || 0) - (b[1].timestamp || 0));

            let removedCount = 0;
            while (cacheEntries.length > removedCount && usageMB + newImageMB > limitMB) {
              const oldestEntry = cacheEntries[removedCount];
              delete cache[oldestEntry[0]];
              removedCount++;
              console.log(`🗑️ Removed cache entry "${oldestEntry[0]}" to make room (${removedCount} removed)`);
            }
          }

          // Update existing cache entry or create new one with pre-compressed image data
          if (cache[dishName]) {
            // Update existing entry
            cache[dishName].imageData = imageData;
            cache[dishName].mimeType = mimeType;
            cache[dishName].timestamp = Date.now();
            if (fileId && !cache[dishName].fileId) {
              cache[dishName].fileId = fileId;
            }

            console.log(`✅ Cache updated for "${dishName}" with pre-compressed image data`);
          } else {
            // Create new cache entry
            cache[dishName] = {
              imageData: imageData,
              mimeType: mimeType,
              timestamp: Date.now(),
              version: '1.0'
            };

            if (fileId) {
              cache[dishName].fileId = fileId;
            }

            console.log(`✅ Cache created for "${dishName}" with pre-compressed image data`);
          }

          chrome.storage.local.set({ imageCache: cache }, () => {
            if (chrome.runtime.lastError) {
              console.error(`❌ Error saving cache for "${dishName}":`, chrome.runtime.lastError);
              resolve(false);
            } else {
              // Debug: Check cache size after update
              const cacheSize = Object.keys(cache).length;
              console.log(`💾 Cache saved successfully. Total entries: ${cacheSize}, Image size: ${imageSizeKB}KB`);

              // Check storage usage
              chrome.storage.local.getBytesInUse(null, (bytesInUse) => {
                const usageMB = Math.round(bytesInUse / (1024 * 1024) * 100) / 100;
                console.log(`📊 Total storage usage: ${usageMB}MB (${bytesInUse} bytes)`);
              });

              resolve(true);
            }
          });
        });
      });
    } catch (error) {
      console.error('❌ Error in updateCacheWithImageData:', error);
      resolve(false);
    }
  });
}

// Listen for messages from popup or background script
chrome.runtime.onMessage.addListener(async (request, sender, sendResponse) => { // Added 'async' here
  if (request.action === 'processPage') {
    console.log('Received request to process page');

    processAllDishesOnPage()
      .then(result => {
        console.log('Sending response to popup:', result);
        sendResponse(result);
      })
      .catch(error => {
        console.error('Error processing page:', error);
        sendResponse({ success: false, message: error.message });
      });

    // Return true to indicate that the response will be sent asynchronously
    return true;
  } else if (request.action === 'stopProcessing') {
    console.log('Received request to stop processing');
    isProcessingStopped = true;
    sendResponse({ success: true, message: 'Stop signal received' });
    return true;
  } else if (request.action === 'updateAutoLoadSetting') {
    console.log(`Received auto-load setting update: ${request.enabled ? 'enabled' : 'disabled'}`);
    autoLoadEnabled = request.enabled;
    sendResponse({ success: true, message: 'Auto-load setting updated' });
    return true;
  } else if (request.action === 'compressImage') {
    // Handle image compression requests from background script
    console.log('🗜️ Received compression request from background script');
    console.log(`📊 Image size to compress: ${Math.round(request.imageData.length / 1024)}KB`);

    if (typeof compressImageWithCanvas === 'function') {
      try {
        const result = await compressImageWithCanvas(request.imageData, request.targetSizeKB || 100);
        console.log('✅ Content script compression completed:', result);
        sendResponse(result);
      } catch (error) {
        console.error('❌ Content script compression failed:', error);
        sendResponse({
          success: false,
          error: 'Content script compression failed',
          details: error.message,
          fallbackData: request.imageData,
          fallbackMimeType: 'image/png'
        });
      }
    } else {
      console.warn('⚠️ Compression function not available in content script');
      sendResponse({
        success: false,
        error: 'Compression function not available',
        fallbackData: request.imageData,
        fallbackMimeType: 'image/png'
      });
    }

    // Return true to indicate we'll send response asynchronously
    return true;
  }
});

// Load auto-load setting from storage
function loadAutoLoadSetting(callback) {
  chrome.storage.sync.get(['autoLoadEnabled'], function(result) {
    autoLoadEnabled = result.autoLoadEnabled !== undefined ? result.autoLoadEnabled : true;
    console.log(`Auto-load setting loaded: ${autoLoadEnabled ? 'enabled' : 'disabled'}`);
    if (callback) callback();
  });
}

// Auto-detect and process dishes when page loads (if auto-load is enabled)
function checkForDishElements() {
  console.log('checkForDishElements() called');
  console.log(`Current autoLoadEnabled value: ${autoLoadEnabled}`);
  console.log(`Page URL: ${window.location.href}`);
  console.log(`Document title: ${document.title}`);

  // Debug: Check what elements exist on the page
  console.log('Debugging DOM structure:');
  console.log('- .dishItemComponent elements:', document.querySelectorAll('.dishItemComponent').length);
  console.log('- .dish-header-button elements:', document.querySelectorAll('.dish-header-button').length);
  console.log('- .venue-dish-header elements:', document.querySelectorAll('.venue-dish-header').length);
  console.log('- .ingredients-list elements:', document.querySelectorAll('.ingredients-list').length);

  const dishElements = findDishElements();
  console.log(`Found ${dishElements.length} dish elements on page`);

  if (dishElements.length > 0) {
    console.log(`Auto-detected ${dishElements.length} dish elements on page load`);
    console.log('Dish elements found:', dishElements);

    if (autoLoadEnabled) {
      console.log('✅ Auto-load is enabled, starting automatic processing in 2 seconds...');
      // Add a small delay to ensure page is fully rendered
      setTimeout(() => {
        console.log('🚀 Starting auto-load processing now...');
        processAllDishesOnPage()
          .then(result => {
            console.log('✅ Auto-load processing completed:', result);
          })
          .catch(error => {
            console.error('❌ Auto-load processing failed:', error);
          });
      }, 2000); // 2 second delay to ensure page is stable
    } else {
      console.log('❌ Auto-load is disabled, skipping automatic processing');
    }
  } else {
    console.log('No dish elements found on this page - auto-load not triggered');
    console.log('Will retry checking for dishes in 5 seconds (in case content loads dynamically)...');

    // Retry after 5 seconds in case the page content loads dynamically
    setTimeout(() => {
      console.log('🔄 Retrying dish detection after 5 seconds...');
      const retryDishElements = findDishElements();
      console.log(`Retry found ${retryDishElements.length} dish elements`);

      if (retryDishElements.length > 0 && autoLoadEnabled) {
        console.log('✅ Found dishes on retry, starting processing...');
        processAllDishesOnPage()
          .then(result => {
            console.log('✅ Retry auto-load processing completed:', result);
          })
          .catch(error => {
            console.error('❌ Retry auto-load processing failed:', error);
          });
      } else if (retryDishElements.length === 0) {
        console.log('Still no dishes found after retry - this page may not have dish content');
      }
    }, 5000);
  }
}

// Set up MutationObserver to detect dynamically loaded content
function setupDynamicContentObserver() {
  console.log('Setting up MutationObserver for dynamic content...');

  const observer = new MutationObserver((mutations) => {
    let shouldCheck = false;

    mutations.forEach((mutation) => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        // Check if any added nodes contain dish elements
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === 1) { // Element node
            if (node.classList && node.classList.contains('dishItemComponent')) {
              shouldCheck = true;
            } else if (node.querySelector && node.querySelector('.dishItemComponent')) {
              shouldCheck = true;
            }
          }
        });
      }
    });

    if (shouldCheck && autoLoadEnabled) {
      console.log('🔄 New dish content detected, checking for auto-load...');
      // Small delay to let the content settle
      setTimeout(() => {
        const newDishElements = findDishElements();
        console.log(`Found ${newDishElements.length} dishes after dynamic content change`);

        if (newDishElements.length > 0) {
          console.log('✅ Processing newly detected dishes...');
          processAllDishesOnPage()
            .then(result => {
              console.log('✅ Dynamic content auto-load completed:', result);
            })
            .catch(error => {
              console.error('❌ Dynamic content auto-load failed:', error);
            });
        }
      }, 1000);
    }
  });

  // Start observing
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });

  console.log('MutationObserver started for dynamic content detection');
}

// Check for dishes when the page is fully loaded
console.log(`Content script initializing... Document ready state: ${document.readyState}`);
console.log(`Current URL: ${window.location.href}`);

if (document.readyState === 'loading') {
  console.log('Document still loading, waiting for DOMContentLoaded...');
  document.addEventListener('DOMContentLoaded', function() {
    console.log('DOMContentLoaded fired, loading auto-load setting...');
    loadAutoLoadSetting(() => {
      checkForDishElements();
      setupDynamicContentObserver();
    });
  });
} else {
  console.log('Document already loaded, loading auto-load setting immediately...');
  loadAutoLoadSetting(() => {
    checkForDishElements();
    setupDynamicContentObserver();
  });
}
