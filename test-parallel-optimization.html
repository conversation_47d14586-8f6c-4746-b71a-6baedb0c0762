<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Parallel Processing Optimization Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .dishItemComponent {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            background: white;
        }
        .venue-dish-header {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .dish-header-button {
            background: none;
            border: none;
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        .ingredients-list {
            color: #666;
            font-size: 14px;
            margin-top: 5px;
        }
        .test-controls {
            margin: 20px 0;
        }
        .test-controls button {
            background: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .test-controls button:hover {
            background: #3367d6;
        }
        .test-controls button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .metrics {
            background: #e8f0fe;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
        }
        .metrics h3 {
            margin-top: 0;
            color: #1a73e8;
        }
        .metric-item {
            margin: 5px 0;
            font-family: monospace;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🚀 Parallel Processing Optimization Test</h1>
        <p>This page simulates the eat.googleplex.com structure to test the optimized parallel image generation.</p>
        
        <div class="test-controls">
            <button id="testBtn">Test Parallel Processing</button>
            <button id="clearBtn">Clear Results</button>
            <button id="stopBtn" disabled>Stop Processing</button>
        </div>

        <div id="status" class="status info">
            Ready to test parallel processing optimization. Click "Test Parallel Processing" to begin.
        </div>

        <div id="metrics" class="metrics" style="display: none;">
            <h3>📊 Performance Metrics</h3>
            <div id="metricsContent"></div>
        </div>
    </div>

    <div class="test-container">
        <h2>🍽️ Test Dishes</h2>
        <p>These mock dishes will be processed using the optimized parallel algorithm:</p>

        <!-- Mock dish elements that simulate eat.googleplex.com structure -->
        <div class="dishItemComponent">
            <div class="venue-dish-header">
                <button class="dish-header-button">Grilled Salmon with Lemon</button>
            </div>
            <div class="ingredients-list">Fresh Atlantic salmon, lemon, herbs, olive oil</div>
        </div>

        <div class="dishItemComponent">
            <div class="venue-dish-header">
                <button class="dish-header-button">Vegetarian Buddha Bowl</button>
            </div>
            <div class="ingredients-list">Quinoa, roasted vegetables, avocado, tahini dressing</div>
        </div>

        <div class="dishItemComponent">
            <div class="venue-dish-header">
                <button class="dish-header-button">Chicken Tikka Masala</button>
            </div>
            <div class="ingredients-list">Chicken breast, tomato sauce, cream, Indian spices</div>
        </div>

        <div class="dishItemComponent">
            <div class="venue-dish-header">
                <button class="dish-header-button">Margherita Pizza</button>
            </div>
            <div class="ingredients-list">Pizza dough, tomato sauce, mozzarella, fresh basil</div>
        </div>

        <div class="dishItemComponent">
            <div class="venue-dish-header">
                <button class="dish-header-button">Caesar Salad</button>
            </div>
            <div class="ingredients-list">Romaine lettuce, parmesan, croutons, caesar dressing</div>
        </div>

        <div class="dishItemComponent">
            <div class="venue-dish-header">
                <button class="dish-header-button">Beef Tacos</button>
            </div>
            <div class="ingredients-list">Ground beef, corn tortillas, lettuce, cheese, salsa</div>
        </div>
    </div>

    <script>
        // Test script to simulate the Chrome extension functionality
        document.addEventListener('DOMContentLoaded', function() {
            const testBtn = document.getElementById('testBtn');
            const clearBtn = document.getElementById('clearBtn');
            const stopBtn = document.getElementById('stopBtn');
            const status = document.getElementById('status');
            const metrics = document.getElementById('metrics');
            const metricsContent = document.getElementById('metricsContent');

            let isProcessing = false;

            testBtn.addEventListener('click', async function() {
                if (isProcessing) return;

                isProcessing = true;
                testBtn.disabled = true;
                stopBtn.disabled = false;
                
                status.className = 'status info';
                status.textContent = 'Starting parallel processing test...';

                try {
                    // Simulate the optimized processing
                    const startTime = Date.now();
                    const result = await simulateParallelProcessing();
                    const endTime = Date.now();
                    const duration = endTime - startTime;

                    // Show results
                    status.className = 'status success';
                    status.textContent = `✅ ${result.message} (Completed in ${duration}ms)`;

                    // Show metrics
                    metrics.style.display = 'block';
                    metricsContent.innerHTML = `
                        <div class="metric-item">⏱️ Total Duration: ${duration}ms</div>
                        <div class="metric-item">📦 Cache Hits: ${result.metrics.cache} (instant)</div>
                        <div class="metric-item">☁️ Drive Retrievals: ${result.metrics.drive}</div>
                        <div class="metric-item">🎨 Generated Images: ${result.metrics.generated}</div>
                        <div class="metric-item">🔄 Retry Attempts: ${result.metrics.retries}</div>
                        <div class="metric-item">⚙️ Batch Size: ${result.metrics.parallelConfig.batchSize}</div>
                        <div class="metric-item">🚀 Max Concurrent: ${result.metrics.parallelConfig.maxConcurrentRequests}</div>
                        <div class="metric-item">⏳ Batch Delay: ${result.metrics.parallelConfig.delayBetweenBatches}ms</div>
                    `;

                } catch (error) {
                    status.className = 'status error';
                    status.textContent = `❌ Test failed: ${error.message}`;
                }

                isProcessing = false;
                testBtn.disabled = false;
                stopBtn.disabled = true;
            });

            clearBtn.addEventListener('click', function() {
                // Clear all generated images and loading indicators
                document.querySelectorAll('.generated-dish-image, .dish-image-loading, .dish-image-error').forEach(el => {
                    el.remove();
                });
                
                status.className = 'status info';
                status.textContent = 'Results cleared. Ready for new test.';
                metrics.style.display = 'none';
            });

            stopBtn.addEventListener('click', function() {
                status.className = 'status info';
                status.textContent = 'Processing stopped by user.';
                isProcessing = false;
                testBtn.disabled = false;
                stopBtn.disabled = true;
            });

            // Simulate the parallel processing algorithm
            async function simulateParallelProcessing() {
                // This would normally call the Chrome extension content script
                // For testing, we'll simulate the behavior
                
                const dishes = document.querySelectorAll('.dishItemComponent');
                const totalDishes = dishes.length;
                
                // Simulate the two-phase processing
                console.log(`🚀 Starting optimized parallel processing for ${totalDishes} dishes`);
                
                // Phase 1: Cache analysis (instant)
                const cacheHits = Math.floor(Math.random() * 3); // Random cache hits
                const uncached = totalDishes - cacheHits;
                
                // Phase 2: Parallel processing simulation
                const batchSize = 3;
                const batches = Math.ceil(uncached / batchSize);
                const retries = Math.floor(Math.random() * 2); // Random retries
                
                // Simulate processing time (much faster with parallel)
                await new Promise(resolve => setTimeout(resolve, 1000 + (batches * 500)));
                
                return {
                    success: true,
                    message: `Processed ${totalDishes} dishes`,
                    metrics: {
                        cache: cacheHits,
                        drive: Math.floor(uncached * 0.6),
                        generated: Math.ceil(uncached * 0.4),
                        retries: retries,
                        skipped: 0,
                        stopped: 0,
                        parallelConfig: {
                            maxConcurrentRequests: 4,
                            batchSize: 3,
                            delayBetweenBatches: 500,
                            generationDelay: 1000
                        }
                    }
                };
            }
        });
    </script>
</body>
</html>
