<!DOCTYPE html>
<html>
<head>
  <title>Eat (with images) Options</title>
  <meta charset="utf-8">
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }

    h1 {
      font-size: 24px;
      margin-bottom: 20px;
    }

    .form-group {
      margin-bottom: 20px;
    }

    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }

    input {
      width: 100%;
      padding: 8px;
      box-sizing: border-box;
      border: 1px solid #ccc;
      border-radius: 4px;
    }

    button {
      background-color: #4285f4;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
      font-weight: bold;
    }

    button:hover {
      background-color: #3367d6;
    }

    .status {
      margin-top: 20px;
      padding: 10px;
      border-radius: 4px;
    }

    .success {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    .error {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    .info {
      background-color: #e2f3fd;
      padding: 15px;
      border-radius: 4px;
      margin-bottom: 20px;
      border-left: 4px solid #4285f4;
    }

    .actions {
      margin-top: 30px;
      display: flex;
      gap: 10px;
    }

    .danger {
      background-color: #dc3545;
    }

    .danger:hover {
      background-color: #bd2130;
    }
  </style>
</head>
<body>
  <h1>Eat (with images) Options</h1>

  <div class="info">
    <h3>🧪 Test Environment</h3>
    <p>Use the comprehensive test environment below to test all extension functionality with real dish data.</p>
  </div>

  <div class="actions">
    <button id="testAllThingsBtn">Test All The Things</button>
    <button id="openTestPageBtn">Open Test Page</button>
  </div>

  <!-- Embedded Test Environment -->
  <div id="testEnvironment" style="margin-top: 30px;">
    <h2>🔥 Live Test Environment</h2>
    <iframe id="testFrame" src="eat_test.html" style="width: 100%; height: 800px; border: 1px solid #ddd; border-radius: 4px;"></iframe>
  </div>

  <div class="test-result" id="testImageResult" style="display: none; margin-top: 20px;">
    <h3>Image Generation Test Result</h3>
    <div id="testImageStatus" class="status"></div>
    <div id="testImageContainer" style="margin-top: 15px; text-align: center;">
      <img id="testImage" style="max-width: 100%; max-height: 300px; border: 1px solid #ddd; border-radius: 4px;" src="" alt="Test generated image">
    </div>
  </div>

  <div class="test-result" id="testCompressionResult" style="display: none; margin-top: 20px;">
    <h3>Image Compression Test Result</h3>
    <div id="testCompressionStatus" class="status"></div>
    <div id="compressionDetails" style="margin-top: 15px;">
      <div style="display: flex; gap: 20px; justify-content: space-around;">
        <div style="text-align: center;">
          <h4>Original Image</h4>
          <div id="originalImageInfo" style="margin-bottom: 10px; font-weight: bold;"></div>
          <img id="originalImage" style="max-width: 200px; max-height: 200px; border: 1px solid #ddd; border-radius: 4px;" src="" alt="Original image">
        </div>
        <div style="text-align: center;">
          <h4>Compressed Image</h4>
          <div id="compressedImageInfo" style="margin-bottom: 10px; font-weight: bold;"></div>
          <img id="compressedImage" style="max-width: 200px; max-height: 200px; border: 1px solid #ddd; border-radius: 4px;" src="" alt="Compressed image">
        </div>
      </div>
      <div id="compressionStats" style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 4px;"></div>
    </div>
  </div>

  <div id="status" class="status" style="display: none;"></div>

  <script src="compression.js"></script>
  <script src="options.js"></script>
</body>
</html>
