# Google Workspace Marketplace SDK Configuration Guide

## Phase 1: Enable Marketplace SDK

### 1.1 Access Google Cloud Console
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. **Select your project:** `eat-with-images`
3. Ensure you're logged in as: `<EMAIL>`

### 1.2 Enable the SDK
1. Navigate to **APIs & Services** > **Library**
2. Search for "Google Workspace Marketplace SDK"
3. Click **Enable** (if not already enabled)
4. Wait for activation (usually takes 1-2 minutes)

### 1.3 Access SDK Configuration
1. Go to **APIs & Services** > **Google Workspace Marketplace SDK**
2. You should see tabs: **Configuration**, **Store Listing**, **Analytics**

## Phase 2: App Configuration

### 2.1 Application Information
Fill out these details in the **Configuration** tab:

```
Application Name: Dish Image Generator
Application Description: Generate and cache images of dishes using Google's Generative AI for eat.googleplex.com
Application URL: [Leave blank or add GitHub repo if you have one]
```

### 2.2 Developer Information
```
Developer Name: Russell Zager
Developer Email: <EMAIL>
Developer Website: [Optional - leave blank if none]
```

### 2.3 Extensions Configuration
**Note:** You'll need your Chrome Extension ID first (from Chrome Web Store upload)

```
Extension ID: [Get this after uploading to Chrome Web Store]
Extension Type: Chrome Extension
```

### 2.4 OAuth Configuration (Already Set)
Verify these match your manifest.json:
```
Client ID: 847291467714-oj6qpf2ct3ebjst2oudpvlersj721079.apps.googleusercontent.com
Scopes:
✅ https://www.googleapis.com/auth/drive.file
✅ https://www.googleapis.com/auth/drive.metadata.readonly
✅ https://www.googleapis.com/auth/cloud-platform
```

## Phase 3: Store Listing Configuration

### 3.1 App Details
```
Language: English
Application Name: Dish Image Generator
Short Description: Generate and cache images of dishes using Google's Generative AI for eat.googleplex.com
Detailed Description: [See detailed description below]
Category: Productivity
Pricing: Free of charge
```

### 3.2 Detailed Description Template
```
Dish Image Generator enhances the eat.googleplex.com experience by automatically generating and caching high-quality images of dishes using Google's advanced Generative AI.

Key Features:
• Automatic image generation for dishes on eat.googleplex.com
• Intelligent caching system for faster loading
• Integration with Google Drive for image storage
• Seamless integration with Google's internal dining platform
• High-quality AI-generated food imagery

Perfect for Google employees who want to see visual representations of dishes before making dining decisions. The extension works exclusively on eat.googleplex.com and requires Google authentication.

Technical Details:
• Uses Google's Imagen AI for image generation
• Stores cached images in Google Drive
• Requires Google Cloud Platform authentication
• Optimized for Google's internal network
```

### 3.3 Graphic Assets Upload
You'll upload these files from your `icons/` folder:

**Application Icons:**
- 32x32: `icons/icon32.png`
- 48x48: `icons/icon48.png`
- 96x96: `icons/icon96.png`
- 128x128: `icons/icon128.png`

**Application Banner:**
- 220x140: `icons/banner220x140.png`

**Screenshots:** [You need to create these - see Phase 4]

### 3.4 Support Links (Required)
You need to create these three pages:

**Terms of Service URL:** [Need to create]
**Privacy Policy URL:** [Need to create]
**Support URL:** [Need to create]

## Phase 4: Create Required Screenshots

### 4.1 Screenshot Requirements
- **Minimum:** 1 screenshot (required)
- **Maximum:** 5 screenshots
- **Size:** At least 1280 x 800 pixels
- **Format:** PNG or JPEG
- **Content:** Must show integration with Google services

### 4.2 Recommended Screenshots
1. **Main functionality:** Extension working on eat.googleplex.com
2. **Image generation:** Showing AI-generated dish images
3. **Popup interface:** Extension popup with options
4. **Settings page:** Extension options/configuration

## Phase 5: Create Support Documentation

### 5.1 Required Pages
You need to create and host these three pages:

1. **Terms of Service**
2. **Privacy Policy** 
3. **Support/Help Page**

### 5.2 Quick Hosting Options
- **GitHub Pages** (free, easy)
- **Google Sites** (free, Google-integrated)
- **Simple static hosting**

## Phase 6: Distribution Settings

### 6.1 Visibility Options
```
Visibility: Public (for marketplace listing)
Regions: All Regions (or specific regions if needed)
```

### 6.2 Domain Restrictions
Your extension is already configured for:
```
Domain: eat.googleplex.com
Type: Internal Google use
```

## Phase 7: Submit for Review

### 7.1 Pre-submission Checklist
- [ ] All icons uploaded and display correctly
- [ ] Screenshots show clear Google integration
- [ ] Support documentation is live and accessible
- [ ] Extension ID added to configuration
- [ ] All required fields completed
- [ ] OAuth consent screen properly configured

### 7.2 Submission Process
1. Click **Publish** in Store Listing
2. Review will take 1-2 weeks typically
3. Monitor email for review updates
4. Address any feedback from Google review team

## Next Steps

1. **First Priority:** Create screenshots of your extension working
2. **Second Priority:** Set up support documentation pages
3. **Third Priority:** Upload to Chrome Web Store to get Extension ID
4. **Final Step:** Complete Marketplace SDK configuration

Would you like me to help you with any specific phase?
