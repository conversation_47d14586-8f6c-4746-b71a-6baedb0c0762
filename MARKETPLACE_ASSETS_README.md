# Google Workspace Marketplace Assets

This document describes the icon variations and assets created for publishing the Dish Image Generator Chrome extension to the Google Workspace Marketplace.

## Created Assets

### Application Icons (Required)
All icons are located in the `icons/` directory and are properly referenced in `manifest.json`:

- **icon16.png** (16 x 16 pixels) - Small icon for browser UI
- **icon32.png** (32 x 32 pixels) - Required for Marketplace listing
- **icon48.png** (48 x 48 pixels) - Required for web apps
- **icon96.png** (96 x 96 pixels) - Required for web apps
- **icon128.png** (128 x 128 pixels) - Main icon for Marketplace listing

### Application Banner (Required)
- **banner220x140.png** (220 x 140 pixels) - Application card banner for Marketplace

## Google Workspace Marketplace Requirements

According to the [official documentation](https://developers.google.com/workspace/marketplace/create-listing), the following graphic assets are required:

### Required Icons
- ✅ 128 x 128 pixels (main listing icon)
- ✅ 32 x 32 pixels (small listing icon)
- ✅ 96 x 96 pixels (required for web apps)
- ✅ 48 x 48 pixels (required for web apps)

### Required Banner
- ✅ 220 x 140 pixels (application card banner)

## Manifest.json Updates

The `manifest.json` file has been updated to include all required icon sizes:

```json
"action": {
  "default_popup": "popup.html",
  "default_icon": {
    "16": "icons/icon16.png",
    "32": "icons/icon32.png",
    "48": "icons/icon48.png",
    "96": "icons/icon96.png",
    "128": "icons/icon128.png"
  }
},
"icons": {
  "16": "icons/icon16.png",
  "32": "icons/icon32.png",
  "48": "icons/icon48.png",
  "96": "icons/icon96.png",
  "128": "icons/icon128.png"
}
```

## Source Image

All icons were generated from the original `Eat_files/eat_icon.png` (192 x 192 pixels) using macOS `sips` command:

```bash
# Create all required icon sizes
sips -z 16 16 Eat_files/eat_icon.png --out icons/icon16.png
sips -z 32 32 Eat_files/eat_icon.png --out icons/icon32.png
sips -z 48 48 Eat_files/eat_icon.png --out icons/icon48.png
sips -z 96 96 Eat_files/eat_icon.png --out icons/icon96.png
sips -z 128 128 Eat_files/eat_icon.png --out icons/icon128.png

# Create application banner
sips -z 140 220 Eat_files/eat_icon.png --out icons/banner220x140.png
```

## Next Steps for Marketplace Publishing

1. **Upload Icons**: Use the created icons when filling out the "Graphic Assets" section in the Google Workspace Marketplace SDK
2. **Upload Banner**: Use `banner220x140.png` for the "Application card banner" field
3. **Additional Requirements**: You'll also need:
   - Screenshots (at least one, 1280 x 800 pixels minimum)
   - App details (name, descriptions, category)
   - Support links (terms of service, privacy policy, support page)

## File Verification

All created files are valid PNG images with correct dimensions:
- icon16.png: 16 x 16 pixels
- icon32.png: 32 x 32 pixels
- icon48.png: 48 x 48 pixels
- icon96.png: 96 x 96 pixels
- icon128.png: 128 x 128 pixels
- banner220x140.png: 220 x 140 pixels

The Chrome extension is now ready with all required icon variations for Google Workspace Marketplace publishing.
