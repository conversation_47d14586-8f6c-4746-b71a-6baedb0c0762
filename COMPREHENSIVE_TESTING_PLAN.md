# Comprehensive Testing Plan for Firebase-Only Migration (v2.0)

## Critical Test Cases

### 1. Cache Inconsistency Resolution (PRIMARY ISSUE)
**Test the core problem that was fixed:**

#### Test Case 1.1: Rainbow Cupcake Specific Test
- [ ] Navigate to eat.googleplex.com
- [ ] Find "Rainbow Cupcake" dish
- [ ] Click to load image
- [ ] **Expected**: Should show actual rainbow cupcake image, NOT mango smoothie
- [ ] **Verify**: Image matches dish name semantically

#### Test Case 1.2: Multiple Dishes with Similar Names
- [ ] Test dishes with similar sanitized names (e.g., "Spicy Chicken" vs "Spicy-Chicken")
- [ ] **Expected**: Each gets unique, appropriate image
- [ ] **Verify**: No image sharing between different dishes

#### Test Case 1.3: Special Characters in Dish Names
- [ ] Test dishes with: apostrophes, parentheses, ampersands, unicode characters
- [ ] Examples: "Chef's Special", "Soup & Salad", "Café Latte"
- [ ] **Expected**: Consistent sanitization, unique images
- [ ] **Verify**: Firebase file names are properly sanitized

### 2. Firebase Storage Integration
#### Test Case 2.1: New Image Generation
- [ ] Find a dish that doesn't exist in Firebase Storage
- [ ] Load the image
- [ ] **Expected**: New image generated and uploaded to Firebase
- [ ] **Verify**: Check Firebase Storage console for new file
- [ ] **Verify**: File named correctly: `{sanitized_dish_name}.jpeg`

#### Test Case 2.2: Existing Image Retrieval
- [ ] Load a dish that already has an image in Firebase Storage
- [ ] **Expected**: Existing image loaded from Firebase (no generation)
- [ ] **Verify**: Console logs show "Found existing image in Firebase Storage"
- [ ] **Verify**: No API calls to image generation service

#### Test Case 2.3: Firebase Authentication
- [ ] Test with fresh browser profile (no cached auth)
- [ ] Load extension and try to load images
- [ ] **Expected**: OAuth flow completes successfully
- [ ] **Verify**: Firebase Storage access granted

### 3. User Interface Changes
#### Test Case 3.1: Firebase Info Button
- [ ] Open extension popup
- [ ] Click "Firebase Info" button (formerly "Clear Cache")
- [ ] **Expected**: Shows Firebase Storage information dialog
- [ ] **Verify**: Contains project info, bucket name, explains no local cache

#### Test Case 3.2: Auto-load Functionality
- [ ] Enable auto-load checkbox in popup
- [ ] Navigate to eat.googleplex.com page
- [ ] **Expected**: Images load automatically on page load
- [ ] **Verify**: All images load from Firebase Storage

### 4. Performance and Reliability
#### Test Case 4.1: Network Interruption
- [ ] Start loading images
- [ ] Disconnect network mid-process
- [ ] Reconnect network
- [ ] **Expected**: Graceful error handling, retry on reconnection
- [ ] **Verify**: No crashes or undefined states

#### Test Case 4.2: Firebase Storage Quota/Limits
- [ ] Generate multiple large images in succession
- [ ] **Expected**: Proper compression applied (target 100KB)
- [ ] **Verify**: No images exceed reasonable size limits
- [ ] **Verify**: Error handling if quota exceeded

#### Test Case 4.3: Concurrent Image Loading
- [ ] Load multiple images simultaneously (5-10 dishes)
- [ ] **Expected**: All images load correctly without conflicts
- [ ] **Verify**: No race conditions or duplicate uploads

### 5. Error Handling and Edge Cases
#### Test Case 5.1: Firebase Storage Unavailable
- [ ] Block Firebase Storage domain in browser
- [ ] Try to load images
- [ ] **Expected**: Clear error messages, graceful degradation
- [ ] **Verify**: Extension doesn't crash

#### Test Case 5.2: Invalid Dish Names
- [ ] Test with empty dish names, very long names, only special characters
- [ ] **Expected**: Proper sanitization and error handling
- [ ] **Verify**: No crashes or invalid file names

#### Test Case 5.3: Image Generation API Failures
- [ ] Test when image generation API returns errors
- [ ] **Expected**: Clear error messages to user
- [ ] **Verify**: No partial uploads to Firebase Storage

### 6. Data Integrity and Consistency
#### Test Case 6.1: File Naming Consistency
- [ ] Generate images for same dish multiple times
- [ ] **Expected**: Same file name used each time
- [ ] **Verify**: No duplicate files created in Firebase Storage

#### Test Case 6.2: Image Quality and Compression
- [ ] Generate various types of images (simple, complex, colorful)
- [ ] **Expected**: All images compressed to reasonable sizes
- [ ] **Verify**: Image quality remains acceptable after compression

#### Test Case 6.3: Cross-Session Persistence
- [ ] Load images in one browser session
- [ ] Close browser, reopen, load same dishes
- [ ] **Expected**: Images load from Firebase Storage (no regeneration)
- [ ] **Verify**: Consistent behavior across sessions

### 7. Regression Testing
#### Test Case 7.1: Existing Firebase Images
- [ ] Test dishes that had images in Firebase Storage before migration
- [ ] **Expected**: All existing images still accessible
- [ ] **Verify**: No broken links or missing images

#### Test Case 7.2: Extension Permissions
- [ ] Verify all required permissions still work
- [ ] Test OAuth flow, storage access, tab access
- [ ] **Expected**: No permission errors or access issues

#### Test Case 7.3: Browser Compatibility
- [ ] Test on Chrome, Edge, other Chromium browsers
- [ ] **Expected**: Consistent behavior across browsers
- [ ] **Verify**: No browser-specific issues

## Testing Tools and Verification

### Console Monitoring
Monitor browser console for:
- [ ] Firebase Storage operation logs
- [ ] Image generation API calls
- [ ] Error messages or warnings
- [ ] Performance timing information

### Firebase Storage Console
Check Firebase Storage console for:
- [ ] New files being created with correct names
- [ ] File sizes are reasonable (target ~100KB)
- [ ] No duplicate or orphaned files
- [ ] Proper folder structure

### Network Tab Monitoring
Monitor network requests for:
- [ ] Firebase Storage API calls
- [ ] Image generation API calls
- [ ] OAuth authentication flows
- [ ] No unnecessary duplicate requests

## Success Criteria

### Primary Success Criteria (Must Pass)
1. **✅ Cache inconsistency resolved**: Different dishes show different, appropriate images
2. **✅ Firebase-only operation**: No local cache dependencies
3. **✅ Reliable image storage**: All images properly stored in Firebase Storage
4. **✅ Consistent file naming**: No duplicate files for same dish

### Secondary Success Criteria (Should Pass)
1. **✅ Performance acceptable**: Images load within reasonable time
2. **✅ Error handling robust**: Graceful handling of network/API issues
3. **✅ User experience smooth**: Clear feedback and intuitive operation
4. **✅ Cross-session consistency**: Reliable behavior across browser sessions

## Test Environment Setup
1. **Fresh browser profile** for clean testing
2. **Access to Firebase Storage console** for verification
3. **Network throttling tools** for performance testing
4. **eat.googleplex.com access** for real-world testing

## Rollback Plan
If critical issues found:
1. Revert to previous version (v1.6)
2. Restore local cache functionality
3. Investigate and fix issues
4. Re-test before re-deployment

This comprehensive testing plan ensures the Firebase-only migration works correctly and resolves the cache inconsistency issues without introducing new problems.
