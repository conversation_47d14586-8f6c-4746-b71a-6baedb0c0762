{"manifest_version": 3, "name": "Eat (with images)", "version": "1.4", "description": "Generate and cache images of dishes using Google's Generative AI with Firebase Storage", "key": "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDONVsKX03hMW/hi4qYW2LBmP8sflYD4tuHqA1wq/HtSLsGZt+nwqricY/Q8eZYQkVzRz828QACZHQMKnycO7JaJXwyejqfa/kmiL2mqGXu7WAiJow1lblWUTYt/IKJSRpjIXqEZisgz5fogIgLfUCSRbst9lgP0aSfehlpaoEygSwrdn8jZiIOxzhYJgEta0MCjUuSC4WEngI4DbbofMZKoxHVjr2LBV6A4Is9HvXa9Lv2KTCkhKQSNz5D5srROfxesOAnhVn0x+P0pkp0VqalnZd08tZASHyulrGZg8WSC7ZuC8u08GZw4qMA5J6KWgxGhBsCenvfA53q7VWpBMh3AgMBAAECggEAXmxVK1s3pLIICq3kgV57Sq4dzohtJ6tmHeioetrpVV+z8DsEF+QObtFUaveRpAF4ntBVlHlnqTDJaNa9p6a4RQocJFsO4/hDps7/lMRQpMcCebDQDCtqqilW11jkhyu3zwPB/8Vu4z/bzgWpBiXM/w26J9JBQcEcEkCNSVWRIofrMUfMzb9Fy6uAt+r8ZFDW4b9ef9auDooQc5Xb7BaxQFZgHw4yQL7Ca3mu0SVFiNax4r3R+OUFFUltndJEFTkV15LZH5xzYN3GA/RRWkPKd/TqO1Tbey1Q+SEH5r7Lc4QtSQkqRBiRp/yzm8ircATml+tsjFiuogC4rZEPqyvvMQKBgQD9//cWim9+rdzUpS/i5eAF1RM0rEK0H/L9+dgsKn0dvbFYPwgjx+QQXEWNsnE1JriXuSAtYMcDel1Pc7TSV8zsNUUd+YYnwxKHJ2XGfyFrZlTV+Mt8AU2kQ+qojJGicsRZNJidWW3poJ4EP0yy38zqxqn9ZCJ55L68sqy+uNPipwKBgQDP1QxfTqgeHRCNW+0GuDS7f53BADpWARoK9r/puzDslhv8bis+U7/ysDeFTuUyMLu4Mbnhe0IWPro4l7KkqTRkwjuXx0za0hvO0ORJxiJ6pz1OW5QodxiZVDCC9IilTjV9as3n8pn2MgfB7ZrYPYf8ZZ/bJCqFx1AE7BfR2DO1sQKBgQDh/IwVlQPQHNZ+p53sbYQdDE/zLFSIuGhUyaco3IDHJlAUaLhQRXfHiqEjiiEpHhNcgox7c0B9+q/skdDO2OltO5QMMEbJN8lTUJ0/tzq/uKNAYQSChxJSVU8BuVeVxjCmAaLLVJbzJ0GQG9WczHwgYrM5zI7UDs4ZS4tggUebvwKBgQCMSdV2KBQXb6sSoHCYzpLZ1cTY2JyIHWpE6FaXIUJfENyHQp5yDfpNiWGQ3LadcIPmS7qbV6Y+ec0eElgGCilRskKjq2ZXduhKbHsMyW/Qtz8Mu4iA4BN8vHWakBIE84vrwsKGxR3kL9VItGiHetFmL/r946XIc8ex6uQRRA1zoQKBgFIkRFQqldkFHtgu8dZzsjeMI4g1JVAf1SCjYliK4lgD0Gtfx1OAgyfRh7GYOdQHl4ssF+svJii25VgOJNsoKOU/b2TVWdHCxZ+oTSKFpcyTj6QHniNzHL1wIfcDroqsP2aYLpvb+vW3U1Zf1Fdiu9o8ktJ81OmlAr63w/0FjNp5", "permissions": ["storage", "identity", "activeTab", "tabs", "offscreen"], "host_permissions": ["https://www.googleapis.com/*", "https://generativelanguage.googleapis.com/*", "https://storage.googleapis.com/*"], "oauth2": {"client_id": "847291467714-1p894uce3afoa33mjv7s9vb46hv64l8v.apps.googleusercontent.com", "scopes": ["https://www.googleapis.com/auth/devstorage.read_write"]}, "action": {"default_popup": "popup.html", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "96": "icons/icon96.png", "128": "icons/icon128.png"}}, "icons": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "96": "icons/icon96.png", "128": "icons/icon128.png"}, "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["https://eat.googleplex.com/*", "file://*/*"], "js": ["compression.js", "Code.js", "content.js"], "run_at": "document_end"}], "options_page": "options.html"}