<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Eat (with images) Bookmarklet</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
        }
        .bookmarklet {
            display: inline-block;
            padding: 10px 15px;
            background-color: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 4px;
            color: #333;
            text-decoration: none;
            font-weight: bold;
            margin: 20px 0;
        }
        .bookmarklet:hover {
            background-color: #e0e0e0;
        }
        .instructions {
            background-color: #f9f9f9;
            border-left: 4px solid #4CAF50;
            padding: 10px 20px;
            margin: 20px 0;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Eat (with images) Bookmarklet</h1>

    <div class="instructions">
        <h2>How to use:</h2>
        <ol>
            <li>Drag the bookmarklet below to your bookmarks bar:</li>
            <p><a href="" id="bookmarkletLink" class="bookmarklet">Eat (with images)</a></p>
            <li>Navigate to a food menu website</li>
            <li>Click the bookmarklet to generate images for dishes on the page</li>
        </ol>
    </div>

    <h2>What it does:</h2>
    <p>This bookmarklet looks for dish elements on a webpage and generates images for them using the Gemini AI API. It works on pages that have elements with the following structure:</p>

    <pre>
&lt;div class="dishItemComponent"&gt;
    &lt;div class="dish-header-button"&gt;Dish Name&lt;/div&gt;
    &lt;div class="ingredients-list"&gt;ingredient1, ingredient2, ingredient3, ...&lt;/div&gt;
    &lt;div class="dish-details"&gt;
        &lt;!-- Generated image will be inserted here --&gt;
        &lt;div class="allergens"&gt;...&lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;
    </pre>

    <h2>Test Area:</h2>
    <p>Below is a sample dish element you can test the bookmarklet on:</p>

    <div class="dishItemComponent" style="border: 1px solid #ddd; padding: 15px; margin: 20px 0; border-radius: 4px;">
        <div class="dish-header-button" style="font-weight: bold; font-size: 18px;">Spaghetti Carbonara</div>
        <div class="ingredients-list">pasta, eggs, pancetta, parmesan cheese, black pepper, olive oil</div>
        <div class="dish-details" style="margin-top: 10px;">
            <div style="color: #666;">A classic Italian pasta dish with a creamy egg sauce.</div>
            <div class="allergens" style="margin-top: 10px; font-size: 14px; color: #999;">Contains: eggs, dairy, gluten</div>
        </div>
    </div>

    <div class="dishItemComponent" style="border: 1px solid #ddd; padding: 15px; margin: 20px 0; border-radius: 4px;">
        <div class="dish-header-button" style="font-weight: bold; font-size: 18px;">Vegetable Curry</div>
        <div class="ingredients-list">potatoes, carrots, peas, onions, garlic, curry powder, coconut milk, vegetable broth</div>
        <div class="dish-details" style="margin-top: 10px;">
            <div style="color: #666;">A flavorful vegetarian curry with mixed vegetables.</div>
            <div class="allergens" style="margin-top: 10px; font-size: 14px; color: #999;">Contains: coconut</div>
        </div>
    </div>

    <script>
        // Load the minified bookmarklet code and set it to the link
        fetch('bookmarklet.min.js')
            .then(response => response.text())
            .then(code => {
                document.getElementById('bookmarkletLink').href = code;
            })
            .catch(error => {
                console.error('Error loading bookmarklet code:', error);
                document.getElementById('bookmarkletLink').href = 'javascript:alert("Error loading bookmarklet code. Please check the console for details.");';
            });
    </script>
</body>
</html>
