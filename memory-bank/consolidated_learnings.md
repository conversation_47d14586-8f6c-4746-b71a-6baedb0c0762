# Consolidated Learnings

## Chrome Extension Development

### Packaging Workflow
A general pattern for packaging Chrome extensions, particularly when managing the development key and versioning:

1.  **Increment Version:** **Crucially, always increment the minor version number** in `manifest.json` (e.g., from "1.2" to "1.3") before proceeding with packaging. This ensures each package has a unique version. The incremented version should persist in the local `manifest.json` for ongoing development.
2.  **Identify Core Files:** Start with `manifest.json` (now with the updated version) to list all essential components (HTML, JavaScript, CSS, icons, service workers, content scripts, etc.). Check linked files (e.g., JS referenced in HTML) to ensure completeness.
3.  **Handle `manifest.json` "key" for Distribution:**
    *   For broader distribution (e.g., Chrome Web Store), it's best practice to temporarily remove the `"key"` field from `manifest.json` before creating the package. This allows the distribution platform (like the Chrome Web Store) to assign its own unique ID to the published extension.
    *   If the key were left in, it could lead to ID conflicts or prevent the store from managing the extension's identity correctly.
4.  **Create the Package:** Use a tool like `zip` to create the archive (e.g., `eat_extension_v1.3.zip`, reflecting the new version) containing all identified files and the modified `manifest.json` (version incremented, key removed).
    *   Example command: `zip -r archive_name_vX.Y.Z.zip manifest.json file1.js dir1/ icons/ ...`
5.  **Restore "key" for Local Development:** After the package is created, revert the "key" removal change to your local `manifest.json` by adding the `"key"` field back. The `manifest.json` will now have the incremented version and the restored key, ready for continued development.

*Rationale:* This process ensures that each distributed package is uniquely versioned and clean for store submission or sharing. It also maintains a consistent extension ID and up-to-date version number in the local development environment.
