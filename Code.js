// Chrome Extension for Dish Image Generation and Caching

// --- Configuration ---
// These values will be stored in Chrome extension storage
const CONFIG = {
  PROJECT_ID: 'eat-with-images', // Google Cloud project ID
  FIREBASE_STORAGE_BUCKET: 'eat-with-images.firebasestorage.app', // Firebase Storage bucket
  FIREBASE_STORAGE_FOLDER: 'dish-images', // Folder within Firebase Storage for dish images
  IMAGEN_ENDPOINT: 'https://generativelanguage.googleapis.com/v1beta/models/imagen-3.0-generate-002:predict', // Default Imagen API endpoint
  CACHE_VERSION: '1.0', // Used for cache versioning
  TARGET_COMPRESSION_KB: 100 // Target compression size in KB for generated images
};

// --- Storage Abstraction Layer ---

/**
 * Abstract Storage Interface
 * Defines the contract that all storage implementations must follow
 */
class StorageInterface {
  /**
   * Upload a file to storage
   * @param {string} fileName - Name of the file
   * @param {string} content - Base64 encoded content
   * @param {string} mimeType - MIME type of the file
   * @param {string} folderId - Folder/container ID
   * @param {string} description - Optional description
   * @returns {Promise<string|null>} File ID if successful, null if failed
   */
  async uploadFile(fileName, content, mimeType, folderId, description = '') {
    throw new Error('uploadFile method must be implemented');
  }

  /**
   * Download a file from storage
   * @param {string} fileId - ID of the file to download
   * @returns {Promise<Object|null>} Object with {base64, mimeType, name} or null if failed
   */
  async downloadFile(fileId) {
    throw new Error('downloadFile method must be implemented');
  }

  /**
   * Search for files in storage
   * @param {string} query - Search query
   * @param {string} fields - Fields to return
   * @param {number} pageSize - Maximum number of results
   * @returns {Promise<Array>} Array of file objects
   */
  async searchFiles(query, fields = 'files(id,name,mimeType)', pageSize = 100) {
    throw new Error('searchFiles method must be implemented');
  }

  /**
   * Get file metadata
   * @param {string} fileId - ID of the file
   * @param {string} fields - Fields to return
   * @returns {Promise<Object|null>} File metadata or null if failed
   */
  async getFileMetadata(fileId, fields = 'id,name,mimeType,size') {
    throw new Error('getFileMetadata method must be implemented');
  }

  /**
   * Delete a file from storage
   * @param {string} fileId - ID of the file to delete
   * @returns {Promise<boolean>} True if successful, false if failed
   */
  async deleteFile(fileId) {
    throw new Error('deleteFile method must be implemented');
  }

  /**
   * List files in a folder/container
   * @param {string} folderId - ID of the folder/container
   * @param {string} fields - Fields to return
   * @param {number} pageSize - Maximum number of results
   * @returns {Promise<Array>} Array of file objects
   */
  async listFiles(folderId, fields = 'files(id,name,mimeType)', pageSize = 100) {
    throw new Error('listFiles method must be implemented');
  }

  /**
   * Check if a file exists
   * @param {string} fileName - Name of the file
   * @param {string} folderId - ID of the folder/container
   * @returns {Promise<Object|null>} File info if exists, null if not found
   */
  async fileExists(fileName, folderId) {
    throw new Error('fileExists method must be implemented');
  }
}

// Storage implementation now exclusively uses Firebase Storage

/**
 * Firebase Storage Implementation
 * Encapsulates all Firebase Storage-specific functionality
 */
class FirebaseStorage extends StorageInterface {
  constructor() {
    super();
    this.baseUrl = `https://storage.googleapis.com/storage/v1/b/${CONFIG.FIREBASE_STORAGE_BUCKET}/o`;
    this.uploadUrl = `https://storage.googleapis.com/upload/storage/v1/b/${CONFIG.FIREBASE_STORAGE_BUCKET}/o`;
    this.lastError = null;
  }

  /**
   * Get authentication token for Firebase Storage
   * @returns {Promise<string|null>} Auth token or null if failed
   */
  async getAuthToken() {
    try {
      return new Promise((resolve, reject) => {
        // Clear any cached tokens first to force fresh authentication
        chrome.identity.getAuthToken({ interactive: false }, (cachedToken) => {
          if (cachedToken) {
            chrome.identity.removeCachedAuthToken({ token: cachedToken }, () => {
              // Now get a fresh token
              chrome.identity.getAuthToken({
                interactive: true,
                scopes: ['https://www.googleapis.com/auth/devstorage.read_write']
              }, (token) => {
                if (chrome.runtime.lastError) {
                  console.log('Auth error:', chrome.runtime.lastError.message);
                  this.lastError = chrome.runtime.lastError.message;
                  resolve(null);
                } else {
                  console.log('✅ Successfully obtained auth token for Firebase Storage');
                  resolve(token);
                }
              });
            });
          } else {
            // No cached token, get fresh one
            chrome.identity.getAuthToken({
              interactive: true,
              scopes: ['https://www.googleapis.com/auth/devstorage.read_write']
            }, (token) => {
              if (chrome.runtime.lastError) {
                console.log('Auth error:', chrome.runtime.lastError.message);
                this.lastError = chrome.runtime.lastError.message;
                resolve(null);
              } else {
                console.log('✅ Successfully obtained auth token for Firebase Storage');
                resolve(token);
              }
            });
          }
        });
      });
    } catch (error) {
      console.log('Error getting auth token:', error.message);
      this.lastError = error.message;
      return null;
    }
  }

  /**
   * Generate Firebase Storage path for a file
   * @param {string} fileName - Name of the file
   * @returns {string} Firebase Storage path
   */
  getStoragePath(fileName) {
    return `${CONFIG.FIREBASE_STORAGE_FOLDER}/${encodeURIComponent(fileName)}`;
  }

  /**
   * Upload a file to Firebase Storage
   * @param {string} fileName - Name of the file
   * @param {string} content - Base64 encoded content
   * @param {string} mimeType - MIME type of the file
   * @param {string} folderId - Not used in Firebase (kept for interface compatibility)
   * @param {string} description - Optional description (stored as metadata)
   * @returns {Promise<string|null>} File path if successful, null if failed
   */
  async uploadFile(fileName, content, mimeType, folderId, description = '') {
    try {
      const token = await this.getAuthToken();
      if (!token) {
        return null;
      }

      const storagePath = this.getStoragePath(fileName);

      // Convert base64 to blob
      const binaryString = atob(content);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      // Use Google Cloud Storage upload API
      const uploadUrl = `${this.uploadUrl}?uploadType=media&name=${encodeURIComponent(storagePath)}`;

      const response = await fetch(uploadUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': mimeType
        },
        body: bytes
      });

      if (response.ok) {
        const result = await response.json();
        console.log(`✅ File uploaded to Firebase Storage: ${storagePath}`);
        return storagePath; // Return the storage path as the file ID
      } else {
        const errorText = await response.text();
        console.log(`❌ Firebase upload failed: ${response.status} - ${errorText}`);
        this.lastError = `Upload failed: ${response.status}`;
        return null;
      }
    } catch (error) {
      console.log('❌ Error uploading to Firebase Storage:', error.message);
      this.lastError = error.message;
      return null;
    }
  }

  /**
   * Download a file from Firebase Storage
   * @param {string} fileId - Storage path of the file
   * @returns {Promise<Object|null>} Object with {base64, mimeType, name} or null if failed
   */
  async downloadFile(fileId) {
    try {
      const token = await this.getAuthToken();
      if (!token) {
        return null;
      }

      const downloadUrl = `${this.baseUrl}/${encodeURIComponent(fileId)}?alt=media`;

      const response = await fetch(downloadUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const arrayBuffer = await response.arrayBuffer();

        // Convert ArrayBuffer to base64 efficiently (avoids call stack overflow for large files)
        const uint8Array = new Uint8Array(arrayBuffer);
        let binaryString = '';
        const chunkSize = 8192; // Process in chunks to avoid call stack overflow

        for (let i = 0; i < uint8Array.length; i += chunkSize) {
          const chunk = uint8Array.slice(i, i + chunkSize);
          binaryString += String.fromCharCode.apply(null, chunk);
        }

        const base64 = btoa(binaryString);
        const mimeType = response.headers.get('content-type') || 'image/jpeg';
        const fileName = fileId.split('/').pop();

        return {
          base64: base64,
          mimeType: mimeType,
          name: fileName
        };
      } else {
        console.log(`❌ Firebase download failed: ${response.status}`);
        this.lastError = `Download failed: ${response.status}`;
        return null;
      }
    } catch (error) {
      console.log('❌ Error downloading from Firebase Storage:', error.message);
      this.lastError = error.message;
      return null;
    }
  }

  /**
   * Search for files in Firebase Storage
   * Note: Firebase Storage doesn't have native search, so we list all files and filter
   * @param {string} query - Search query (simplified for Firebase)
   * @param {string} fields - Fields to return (ignored for Firebase)
   * @param {number} pageSize - Maximum number of results
   * @returns {Promise<Array>} Array of file objects
   */
  async searchFiles(query, fields = 'files(id,name,mimeType)', pageSize = 100) {
    try {
      const token = await this.getAuthToken();
      if (!token) {
        return [];
      }

      // List all files in the dish-images folder using Google Cloud Storage API
      const listUrl = `https://storage.googleapis.com/storage/v1/b/${CONFIG.FIREBASE_STORAGE_BUCKET}/o?prefix=${encodeURIComponent(CONFIG.FIREBASE_STORAGE_FOLDER + '/')}&maxResults=${pageSize}`;

      const response = await fetch(listUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const result = await response.json();
        const items = result.items || [];

        // Filter based on query (simplified search)
        const filteredItems = items.filter(item => {
          const fileName = item.name.split('/').pop();
          // Extract dish name from query (simplified)
          const dishNameMatch = query.match(/name = '([^']+)'/);
          if (dishNameMatch) {
            const searchName = dishNameMatch[1];
            return fileName.toLowerCase().includes(searchName.toLowerCase());
          }
          return true;
        });

        // Convert to standard format for compatibility
        return filteredItems.map(item => ({
          id: item.name, // Use full path as ID
          name: item.name.split('/').pop(),
          mimeType: item.contentType || 'image/jpeg',
          createdTime: item.timeCreated,
          size: item.size
        }));
      } else {
        console.log(`❌ Firebase search failed: ${response.status}`);
        this.lastError = `Search failed: ${response.status}`;
        return [];
      }
    } catch (error) {
      console.log('❌ Error searching Firebase Storage:', error.message);
      this.lastError = error.message;
      return [];
    }
  }

  /**
   * Get file metadata from Firebase Storage
   * @param {string} fileId - Storage path of the file
   * @param {string} fields - Fields to return (ignored for Firebase)
   * @returns {Promise<Object|null>} File metadata or null if failed
   */
  async getFileMetadata(fileId, fields = 'id,name,mimeType,size') {
    try {
      const token = await this.getAuthToken();
      if (!token) {
        return null;
      }

      const metadataUrl = `${this.baseUrl}/${encodeURIComponent(fileId)}`;

      const response = await fetch(metadataUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const metadata = await response.json();
        return {
          id: metadata.name,
          name: metadata.name.split('/').pop(),
          mimeType: metadata.contentType || 'image/jpeg',
          size: metadata.size,
          createdTime: metadata.timeCreated
        };
      } else {
        console.log(`❌ Firebase metadata fetch failed: ${response.status}`);
        this.lastError = `Metadata fetch failed: ${response.status}`;
        return null;
      }
    } catch (error) {
      console.log('❌ Error getting Firebase Storage metadata:', error.message);
      this.lastError = error.message;
      return null;
    }
  }

  /**
   * Delete a file from Firebase Storage
   * @param {string} fileId - Storage path of the file to delete
   * @returns {Promise<boolean>} True if successful, false if failed
   */
  async deleteFile(fileId) {
    try {
      const token = await this.getAuthToken();
      if (!token) {
        return false;
      }

      const deleteUrl = `${this.baseUrl}/${encodeURIComponent(fileId)}`;

      const response = await fetch(deleteUrl, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        console.log(`✅ File deleted from Firebase Storage: ${fileId}`);
        return true;
      } else {
        console.log(`❌ Firebase delete failed: ${response.status}`);
        this.lastError = `Delete failed: ${response.status}`;
        return false;
      }
    } catch (error) {
      console.log('❌ Error deleting from Firebase Storage:', error.message);
      this.lastError = error.message;
      return false;
    }
  }

  /**
   * List files in Firebase Storage folder
   * @param {string} folderId - Not used in Firebase (kept for interface compatibility)
   * @param {string} fields - Fields to return (ignored for Firebase)
   * @param {number} pageSize - Maximum number of results
   * @returns {Promise<Array>} Array of file objects
   */
  async listFiles(folderId, fields = 'files(id,name,mimeType)', pageSize = 100) {
    try {
      const token = await this.getAuthToken();
      if (!token) {
        return [];
      }

      const listUrl = `https://storage.googleapis.com/storage/v1/b/${CONFIG.FIREBASE_STORAGE_BUCKET}/o?prefix=${encodeURIComponent(CONFIG.FIREBASE_STORAGE_FOLDER + '/')}&maxResults=${pageSize}`;

      const response = await fetch(listUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const result = await response.json();
        const items = result.items || [];

        return items.map(item => ({
          id: item.name,
          name: item.name.split('/').pop(),
          mimeType: item.contentType || 'image/jpeg',
          createdTime: item.timeCreated,
          size: item.size
        }));
      } else {
        console.log(`❌ Firebase list failed: ${response.status}`);
        this.lastError = `List failed: ${response.status}`;
        return [];
      }
    } catch (error) {
      console.log('❌ Error listing Firebase Storage files:', error.message);
      this.lastError = error.message;
      return [];
    }
  }

  /**
   * Check if a file exists in Firebase Storage
   * @param {string} fileName - Name of the file
   * @param {string} folderId - Not used in Firebase (kept for interface compatibility)
   * @returns {Promise<Object|null>} File info if exists, null if not found
   */
  async fileExists(fileName, folderId) {
    try {
      const storagePath = this.getStoragePath(fileName);
      const metadata = await this.getFileMetadata(storagePath);
      return metadata;
    } catch (error) {
      console.log('Error checking file existence:', error.message);
      return null;
    }
  }
}

/**
 * Storage Factory
 * Creates and manages storage instances
 */
class StorageFactory {
  static instance = null;

  /**
   * Get the current storage instance
   * @returns {StorageInterface} Storage instance
   */
  static getStorage() {
    if (!this.instance) {
      // Switch to Firebase Storage (requires OAuth configuration)
      this.instance = new FirebaseStorage();
    }
    return this.instance;
  }

  /**
   * Set a custom storage instance (for testing or alternative providers)
   * @param {StorageInterface} storage - Storage instance
   */
  static setStorage(storage) {
    this.instance = storage;
  }
}

// --- Chrome Extension Initialization ---
// These functions are not needed in the Chrome extension as they're replaced by the popup UI
// The original Google Apps Script UI functions are removed since they're not compatible with Chrome extensions

// Initialize the extension
function initializeExtension() {
  console.log('Initializing Eat (with images) extension');

  // Firebase Storage configuration is hardcoded in CONFIG.
  // Check if imagenEndpoint is set, use default if not.
  chrome.storage.sync.get(['imagenEndpoint'], function(data) {
    const updates = {};
    if (!data.imagenEndpoint) {
      updates.imagenEndpoint = CONFIG.IMAGEN_ENDPOINT;
      console.log(`Setting default Imagen endpoint: ${CONFIG.IMAGEN_ENDPOINT}`);
    }

    if (Object.keys(updates).length > 0) {
      chrome.storage.sync.set(updates, function() {
        if (chrome.runtime.lastError) {
          console.error('Error saving default imagenEndpoint:', chrome.runtime.lastError.message);
        } else {
          console.log('Default imagenEndpoint configuration value saved.');
        }
      });
    }
    console.log('Extension initialized. Using Firebase Storage for image storage.');
  });

  return true;
}

// Get extension information
function getExtensionInfo() {
  return {
    name: 'Eat (with images)',
    version: '1.1',
    description: 'This Chrome extension provides image generation using Imagen API with OAuth, caching, and storage functionality for dish images.',
    author: 'Your Name',
    features: [
      'OAuth authentication for API access',
      'Imagen 4.0 model for image generation',
      'Firebase Storage for scalable image storage',
      'Image caching for improved performance',
      'Configurable Imagen API endpoint'
    ]
  };
}

// Request deduplication to prevent duplicate processing
const activeRequests = new Map();

// --- Main Function (Callable from Chrome Extension) ---
async function getOrGenerateDishImage(dishName, ingredients, model) {
  if (!dishName) {
    return { success: false, error: 'Dish name is required.' };
  }

  // Check if there's already an active request for this dish
  const requestKey = dishName.toLowerCase().trim();
  if (activeRequests.has(requestKey)) {
    console.log(`⏳ Request for "${dishName}" already in progress, waiting for completion...`);
    return await activeRequests.get(requestKey);
  }

  // Create a promise for this request and store it
  const requestPromise = processImageRequest(dishName, ingredients, model);
  activeRequests.set(requestKey, requestPromise);

  try {
    const result = await requestPromise;
    return result;
  } finally {
    // Clean up the active request
    activeRequests.delete(requestKey);
  }
}

// Helper function to add a timeout to a promise
function withTimeout(promise, ms, timeoutMessage = 'Operation timed out') {
  let timeoutId;
  const timeout = new Promise((resolve, reject) => {
    timeoutId = setTimeout(() => {
      reject(new Error(timeoutMessage));
    }, ms);
  });

  return Promise.race([promise, timeout]).finally(() => {
    clearTimeout(timeoutId);
  });
}

async function processImageRequest(dishName, ingredients, model) {
  try {
    console.log(`🔄 Processing request for dish: ${dishName}`);
    console.log(`Model specified: ${model || 'default'}`);

    // Apply a timeout to the entire image processing flow
    const TIMEOUT_MS = 25000; // 25 seconds
    const result = await withTimeout(
      (async () => {

    // If no model is specified, use 'imagen-3.0-generate-002' as the default
    if (!model) {
      model = 'imagen-3.0-generate-002';
      console.log(`Using default model: ${model}`);
    }

    // Get storage instance
    const storage = StorageFactory.getStorage();

    // 1. Check Firebase Storage directly (no local cache)
    console.log(`🔍 Checking Firebase Storage for existing image of "${dishName}"`);
    const firebaseResult = await findImageInFirebase(dishName, storage);

    if (firebaseResult) {
      console.log(`✅ Found existing image in Firebase Storage for "${dishName}"`);
      return {
        success: true,
        source: 'firebase',
        imageData: firebaseResult.imageData,
        mimeType: firebaseResult.mimeType,
        dishName: dishName,
        fileId: firebaseResult.fileId
      };
    }

    // 2. No existing image found: Generate New Image
    console.log(`No existing image found for "${dishName}". Generating new image...`);

    // Generate new image using AI
    const folderId = CONFIG.FIREBASE_STORAGE_FOLDER; // Use Firebase storage folder
    const prompt = buildPrompt(dishName, ingredients);
    const generatedImageData = await generateImageWithGoogleAPI(prompt, model);

    if (generatedImageData && generatedImageData.success) {
      // CRITICAL: Always use compressed data to prevent 1MB+ files
      const compressedData = generatedImageData.imageData; // This is now the compressed data
      const compressedMimeType = generatedImageData.mimeType; // This is now JPEG
      const originalData = generatedImageData.originalImageData || generatedImageData.imageData;
      const originalMimeType = generatedImageData.originalMimeType || generatedImageData.mimeType;

      console.log(`💾 Saving compressed image to Firebase Storage (${Math.round(compressedData.length / 1024)}KB ${compressedMimeType})`);

      if (generatedImageData.compressionFailed) {
        console.warn(`⚠️ WARNING: Compression failed - ${generatedImageData.compressionReason}`);
      }

      // Save compressed image to Firebase Storage
      const newFileId = await saveImageToStorage(compressedData, dishName, compressedMimeType, storage, folderId);

      if (newFileId) {
        console.log(`✅ Successfully saved and returning new image for "${dishName}"`);
        return {
          success: true,
          source: 'generated',
          imageData: compressedData, // Return compressed data for immediate use
          mimeType: compressedMimeType,
          dishName: dishName,
          fileId: newFileId,
          originalImageData: originalData, // Include original for reference
          originalMimeType: originalMimeType,
          originalSize: generatedImageData.originalSize,
          compressedSize: generatedImageData.compressedSize
        };
      } else {
        return {
          success: false,
          error: 'Failed to save generated image to Firebase Storage.'
        };
      }
    } else {
      return {
        success: false,
        error: 'Failed to generate image from API.',
        details: generatedImageData ? generatedImageData.error : 'Unknown API error.'
      };
    }

    })(),
    TIMEOUT_MS,
    `Image generation/retrieval for "${dishName}" timed out after ${TIMEOUT_MS / 1000} seconds.`
    );
    return result;
  } catch (error) {
    console.log('Error in processImageRequest (with timeout): ' + error.message);
    return {
      success: false,
      error: 'Error processing request.',
      details: error.message
    };
  }
}

// --- Helper Functions ---

// Builds the prompt string
function buildPrompt(dishName, ingredients) {
  // Enhanced prompt for Imagen image generation
  const BASE_PROMPT = "professional photo of {dish_name} made with {ingredients} on a table in a restaurant. Natural lighting, close-up shot from a slightly high angle. shallow dof. no distractions, no text, no watermarks, photorealistic, professional food photography";

  let prompt = BASE_PROMPT.replace('{dish_name}', dishName);
  prompt = prompt.replace('{ingredients}', ingredients ? ingredients : ''); // Use ingredients if provided

  return prompt;
}


// Firebase Storage helper functions (replacing local cache)
const FirebaseImageStorage = {
  // Check if an image exists in Firebase Storage for a dish
  async imageExists(dishName, storage) {
    try {
      const sanitizedDishName = dishName.replace(/[^a-zA-Z0-9_\-. ]/g, '_').trim();
      const fileName = `${sanitizedDishName}.jpeg`;

      console.log(`🔍 Checking Firebase Storage for "${dishName}" (file: ${fileName})`);

      const fileInfo = await storage.fileExists(fileName);
      if (fileInfo) {
        console.log(`✅ Found existing image in Firebase Storage for "${dishName}"`);
        // Return the storage path (fileId) that downloadFile expects
        const storagePath = storage.getStoragePath(fileName);
        return { exists: true, fileName: fileName, fileId: storagePath };
      } else {
        console.log(`❌ No existing image found in Firebase Storage for "${dishName}"`);
        return { exists: false, fileName: fileName };
      }
    } catch (error) {
      console.error(`❌ Error checking Firebase Storage for "${dishName}":`, error);
      return { exists: false, fileName: null };
    }
  },

  // Download an image from Firebase Storage
  async downloadImage(fileId, storage) {
    try {
      console.log(`📥 Downloading image from Firebase Storage: ${fileId}`);
      const fileData = await storage.downloadFile(fileId);

      if (fileData && fileData.base64) {
        console.log(`✅ Successfully downloaded image from Firebase Storage: ${fileId}`);
        return {
          imageData: fileData.base64,
          mimeType: fileData.mimeType || 'image/jpeg'
        };
      } else {
        console.error(`❌ Failed to download image from Firebase Storage: ${fileId}`);
        return null;
      }
    } catch (error) {
      console.error(`❌ Error downloading image from Firebase Storage: ${fileId}`, error);
      return null;
    }
  }
};

// Finds an image in Firebase Storage by dish name (replaces local cache lookup)
async function findImageInFirebase(dishName, storage) {
  try {
    console.log(`🔍 Looking for image in Firebase Storage for "${dishName}"`);

    // Check if image exists in Firebase Storage
    const existsResult = await FirebaseImageStorage.imageExists(dishName, storage);

    if (existsResult.exists) {
      // Download the image from Firebase Storage using the fileId (storage path)
      const imageData = await FirebaseImageStorage.downloadImage(existsResult.fileId, storage);

      if (imageData) {
        console.log(`✅ Successfully retrieved image from Firebase Storage for "${dishName}"`);
        return {
          imageData: imageData.imageData,
          mimeType: imageData.mimeType,
          fileId: existsResult.fileId,
          source: 'firebase'
        };
      } else {
        console.error(`❌ Failed to download image from Firebase Storage for "${dishName}"`);
        return null;
      }
    } else {
      console.log(`❌ No image found in Firebase Storage for "${dishName}"`);
      return null;
    }
  } catch (error) {
    console.error(`❌ Error looking for image in Firebase Storage for "${dishName}":`, error);
    return null;
  }
}

// Storage abstraction wrapper functions for backward compatibility

/**
 * Save an image to storage using the storage abstraction
 * @param {string} base64Data - Base64 encoded image data
 * @param {string} dishName - Name of the dish
 * @param {string} mimeType - MIME type of the image
 * @param {StorageInterface} storage - Storage instance
 * @param {string} folderId - Folder/container ID
 * @returns {Promise<string|null>} File ID if successful, null if failed
 */
async function saveImageToStorage(base64Data, dishName, mimeType, storage, folderId) {
  try {
    // CRITICAL SAFETY CHECK: Ensure ALL images are compressed before saving
    const imageSizeKB = Math.round(base64Data.length / 1024);
    const maxSizeKB = 200; // Reasonable limit for Firebase storage

    // Force compression for ANY image over 150KB OR any PNG file
    const needsCompression = imageSizeKB > 150 || mimeType.includes('png');

    if (needsCompression) {
      console.log(`🗜️ CRITICAL COMPRESSION: ${imageSizeKB}KB ${mimeType} for "${dishName}" - MUST compress to prevent 1MB+ files`);

      // Force compression regardless of original size
      const compressed = await compressImageData(base64Data, 100); // Target 100KB

      if (compressed && !compressed.compressionSkipped) {
        const compressedSizeKB = Math.round(compressed.data.length / 1024);
        console.log(`✅ FORCED COMPRESSION SUCCESS: ${imageSizeKB}KB → ${compressedSizeKB}KB`);
        base64Data = compressed.data;
        mimeType = compressed.mimeType; // Should be JPEG
      } else {
        console.error(`❌ CRITICAL: Compression failed for "${dishName}" - ${compressed?.reason}`);
        console.error(`❌ THIS WILL CREATE A ${imageSizeKB}KB ${mimeType} FILE`);

        // Last resort: force JPEG format even without compression
        if (mimeType.includes('png')) {
          console.log(`🚨 EMERGENCY: Converting PNG to JPEG format to reduce file size`);
          mimeType = 'image/jpeg';
        }
      }
    } else {
      console.log(`✅ Image size acceptable: ${imageSizeKB}KB ${mimeType} for "${dishName}"`);
    }

    // Sanitize dish name for use as a filename (remove invalid characters)
    const sanitizedDishName = dishName.replace(/[^a-zA-Z0-9_\-. ]/g, '_');

    // Create a standardized filename format: just the dish name with extension
    // No timestamp to allow for reuse of the same image
    const fileExtension = mimeType.split('/')[1] || 'png';
    const fileName = `${sanitizedDishName}.${fileExtension}`;

    console.log(`💾 Saving image to storage with filename: ${fileName} (${mimeType})`);
    console.log(`📊 Image size: ${Math.round(base64Data.length / 1024)}KB`);
    console.log(`Original dish name: ${dishName}, Sanitized dish name: ${sanitizedDishName}`);

    // Check if a file with this exact name already exists to prevent duplicates
    const existingFile = await storage.fileExists(fileName, folderId);

    if (existingFile) {
      // File with this name already exists, check its size
      const existingMetadata = await storage.getFileMetadata(existingFile.id, 'id,name,size');
      const existingSizeKB = existingMetadata?.size ? Math.round(existingMetadata.size / 1024) : 'unknown';

      console.log(`✅ File with name ${fileName} already exists with ID: ${existingFile.id} (size: ${existingSizeKB}KB)`);

      // If existing file is much larger than our compressed version, consider replacing it
      if (existingMetadata?.size && existingMetadata.size > base64Data.length * 1.5) {
        console.log(`🔄 Existing file is significantly larger (${existingSizeKB}KB vs ${Math.round(base64Data.length / 1024)}KB). Consider replacing.`);
        // For now, just log this - could implement replacement logic later
      }

      return existingFile.id;
    } else {
      // No existing file, create a new one
      const description = `Dish image for: ${dishName}`;
      console.log(`📤 Uploading new file: ${fileName} (${Math.round(base64Data.length / 1024)}KB)`);

      const fileId = await storage.uploadFile(fileName, base64Data, mimeType, folderId, description);

      if (fileId) {
        console.log(`✅ File uploaded successfully with ID: ${fileId}`);
      } else {
        console.log(`❌ Failed to upload file: ${fileName}`);
      }

      return fileId;
    }
  } catch (error) {
    console.log('❌ Error in saveImageToStorage: ' + error.message);
    return null;
  }
}

/**
 * Gets an image file from storage by ID (backward compatibility wrapper)
 * @param {string} fileId - ID of the file to download
 * @returns {Promise<Object|null>} Object with {base64, mimeType, name} or null if failed
 */
async function getImageFromStorage(fileId) {
  try {
    const storage = StorageFactory.getStorage();
    return await storage.downloadFile(fileId);
  } catch (error) {
    console.log('Error in getImageFromStorage: ' + error.message);
    return null;
  }
}

// --- Offscreen Document Management for Compression ---
const OFFSCREEN_DOCUMENT_PATH = 'offscreen.html';
let creatingOffscreenDocument = null; // Promise to prevent multiple creation attempts

async function hasOffscreenDocument() {
  if (chrome.runtime.getContexts) {
    const contexts = await chrome.runtime.getContexts({
      contextTypes: ['OFFSCREEN_DOCUMENT'],
      documentUrls: [chrome.runtime.getURL(OFFSCREEN_DOCUMENT_PATH)]
    });
    return !!contexts && contexts.length > 0;
  }
  if (chrome.offscreen && chrome.offscreen.hasDocument) {
     return await chrome.offscreen.hasDocument();
  }
  return false;
}

async function setupOffscreenDocument() {
  if (await hasOffscreenDocument()) {
    console.log('[OffscreenManager] Document already exists.');
    return;
  }
  if (creatingOffscreenDocument) {
    await creatingOffscreenDocument;
    return;
  }
  console.log('[OffscreenManager] Creating offscreen document...');
  creatingOffscreenDocument = chrome.offscreen.createDocument({
    url: OFFSCREEN_DOCUMENT_PATH,
    reasons: [chrome.offscreen.Reason.DOM_PARSER],
    justification: 'Image compression requires DOM APIs (Canvas).',
  });
  try {
    await creatingOffscreenDocument;
    console.log('[OffscreenManager] Offscreen document created successfully.');
  } catch (error) {
    console.error('[OffscreenManager] Error creating offscreen document:', error);
  } finally {
    creatingOffscreenDocument = null;
  }
}

// Compress image data for more efficient storage and caching
async function compressImageData(base64Data, targetSizeKB = 100) {
  try { // Outer try for the whole function
    console.log(`[compressImageData] Attempting compression. Target: ${targetSizeKB}KB`);

    // Check if we're in a context that has direct DOM access
    if (typeof window !== 'undefined' && typeof document !== 'undefined' && typeof compressImageWithCanvas === 'function') {
      console.log('[compressImageData] DOM context detected. Using compressImageWithCanvas directly.');
      try {
        const result = await compressImageWithCanvas(base64Data, targetSizeKB);
        if (result.success) {
          return {
            data: result.data,
            mimeType: result.mimeType,
            originalSize: result.originalSize,
            compressedSize: result.compressedSize,
            quality: result.quality,
            compressionSkipped: false,
            ...result
          };
        } else {
          console.warn('[compressImageData] Direct canvas compression failed, using fallback.', result.error);
          return {
            data: result.fallbackData || base64Data,
            mimeType: result.fallbackMimeType || 'image/png',
            originalSize: Math.round((result.fallbackData || base64Data).length / 1024),
            compressedSize: Math.round((result.fallbackData || base64Data).length / 1024),
            compressionSkipped: true,
            reason: result.error || 'Direct canvas compression failed'
          };
        }
      } catch (error) {
          console.error('[compressImageData] Error during direct canvas compression:', error);
          // This catch is for unexpected errors in the direct call itself, not for `success:false` from compressImageWithCanvas
          return {
              data: base64Data,
              mimeType: 'image/png',
              originalSize: Math.round(base64Data.length / 1024),
              compressedSize: Math.round(base64Data.length / 1024),
              compressionSkipped: true,
              reason: `Direct canvas compression exception: ${error.message}`
          };
      }
    } else if (chrome.offscreen) { // Service worker context, use offscreen document
      console.log('[compressImageData] Service worker context detected. Attempting to use offscreen document for compression.');
      try {
        if (!await hasOffscreenDocument()) {
          await setupOffscreenDocument();
          await new Promise(resolve => setTimeout(resolve, 500)); // Ensure document is ready
        }
        if (!await hasOffscreenDocument()) {
          console.error('[compressImageData] Failed to create or find offscreen document after setup.');
          throw new Error('Offscreen document setup failed.');
        }

        console.log('[compressImageData] Sending message to offscreen document for compression...');
        const response = await chrome.runtime.sendMessage({
          action: 'compressImageOffscreen',
          imageData: base64Data,
          targetSizeKB: targetSizeKB,
        });
        console.log('[compressImageData] Received response from offscreen document:', response);

        if (response && response.success) {
          return {
            data: response.data,
            mimeType: response.mimeType,
            originalSize: response.originalSize,
            compressedSize: response.compressedSize,
            quality: response.quality,
            compressionSkipped: false,
            ...response
          };
        } else {
          console.warn('[compressImageData] Offscreen document compression failed. Reason:', response?.error, response?.details);
          return {
            data: response?.fallbackData || base64Data,
            mimeType: response?.fallbackMimeType || 'image/png',
            originalSize: Math.round((response?.fallbackData || base64Data).length / 1024),
            compressedSize: Math.round((response?.fallbackData || base64Data).length / 1024),
            compressionSkipped: true,
            reason: response?.error || 'Offscreen compression failed'
          };
        }
      } catch (error) {
        console.error('[compressImageData] Error using offscreen document for compression:', error);
        return {
          data: base64Data,
          mimeType: 'image/png',
          originalSize: Math.round(base64Data.length / 1024),
          compressedSize: Math.round(base64Data.length / 1024),
          compressionSkipped: true,
          reason: `Offscreen document communication error: ${error.message}`
        };
      }
    } else {
      // Fallback if no known compression method is available
      console.error('[compressImageData] Unknown context or chrome.offscreen API not available. Cannot compress.');
      return {
        data: base64Data,
        mimeType: 'image/png',
        originalSize: Math.round(base64Data.length / 1024),
        compressedSize: Math.round(base64Data.length / 1024),
        compressionSkipped: true,
        reason: 'No compression utility available or unknown context'
      };
    }
  } catch (error) { // Outer catch for the entire function
    console.error('⚠️ Image compression error (outer):', error);
    return {
      data: base64Data,
      mimeType: 'image/png',
      originalSize: Math.round(base64Data.length / 1024),
      compressedSize: Math.round(base64Data.length / 1024),
      compressionSkipped: true,
      reason: `Outer exception in compressImageData: ${error.message}`
    };
  }
}

// Exponential backoff configuration
const RETRY_CONFIG = {
  initialDelay: 1000,      // 1 second initial delay
  maxRetries: 5,           // Maximum 5 retry attempts
  backoffMultiplier: 2,    // 2x backoff multiplier
  maxDelay: 30000,         // 30 second maximum delay
  jitterFactor: 0.1        // 10% jitter to avoid thundering herd
};

// Helper function to add jitter to delay
function addJitter(delay, jitterFactor = RETRY_CONFIG.jitterFactor) {
  const jitter = delay * jitterFactor * (Math.random() * 2 - 1); // ±jitterFactor
  return Math.max(0, delay + jitter);
}

// Helper function to calculate exponential backoff delay
function calculateBackoffDelay(attempt, config = RETRY_CONFIG) {
  const baseDelay = config.initialDelay * Math.pow(config.backoffMultiplier, attempt);
  const cappedDelay = Math.min(baseDelay, config.maxDelay);
  return addJitter(cappedDelay, config.jitterFactor);
}

// Helper function to determine if error is retryable
function isRetryableError(error, response) {
  // Check for quota errors (429)
  if (response && response.status === 429) {
    return { retryable: true, type: 'quota', reason: 'Rate limit exceeded' };
  }

  // Check for server errors (5xx)
  if (response && response.status >= 500) {
    return { retryable: true, type: 'server', reason: 'Server error' };
  }

  // Check for network errors
  if (error && (error.name === 'NetworkError' || error.message.includes('network') || error.message.includes('fetch'))) {
    return { retryable: true, type: 'network', reason: 'Network error' };
  }

  // Check for timeout errors
  if (error && (error.name === 'TimeoutError' || error.message.includes('timeout'))) {
    return { retryable: true, type: 'timeout', reason: 'Request timeout' };
  }

  // Authentication errors (401/403) - not retryable
  if (response && (response.status === 401 || response.status === 403)) {
    return { retryable: false, type: 'auth', reason: 'Authentication error' };
  }

  // Client errors (4xx except 429) - not retryable
  if (response && response.status >= 400 && response.status < 500 && response.status !== 429) {
    return { retryable: false, type: 'client', reason: 'Client error' };
  }

  // Unknown errors - not retryable by default
  return { retryable: false, type: 'unknown', reason: 'Unknown error' };
}

// Generates an image using the Google Generative Language API with exponential backoff
function generateImageWithGoogleAPI(prompt, model, retryAttempt = 0) {
  return new Promise(async (resolve) => {
    console.log(`Generating image with model: ${model} (attempt ${retryAttempt + 1}/${RETRY_CONFIG.maxRetries + 1})`);
    console.log(`Prompt: ${prompt}`);

    // For testing purposes, return a simple 1x1 transparent PNG if no real API call is needed
    if (model === 'test-model') {
      console.log('Using test model - returning sample image');
      const testImageData = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==';

      // Apply compression to test image as well
      const compressed = await compressImageData(testImageData, CONFIG.TARGET_COMPRESSION_KB);

      resolve({
        success: true,
        imageData: testImageData,
        mimeType: 'image/png',
        compressedImageData: compressed.data,
        compressedMimeType: compressed.mimeType,
        originalSize: compressed.originalSize,
        compressedSize: compressed.compressedSize
      });
      return;
    }

    // Get the Imagen endpoint from storage or use default
    chrome.storage.sync.get(['imagenEndpoint'], async (result) => {
      const imagenEndpoint = result.imagenEndpoint || CONFIG.IMAGEN_ENDPOINT;

      // Use the specified Imagen API endpoint with API key (like the bookmarklet)
      const API_KEY = 'AIzaSyAbLkAK41p8jeOPN7uCm4I_rXDJuw97Z7o';
      const fetchUrl = `${imagenEndpoint}?key=${API_KEY}`;

      const requestBody = {
        instances: [{
          prompt: prompt
        }],
        parameters: {
          sampleCount: 1
        }
      };

      console.log('Using Imagen API endpoint with API key');

      // Make API request directly with API key (like the bookmarklet)
      console.log('Sending API request with API key...');

      try {
        const response = await fetch(fetchUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(requestBody)
        });

        console.log(`API Response Status: ${response.status}`);

        // Check if response is not ok and handle retryable errors
        if (!response.ok) {
          const errorText = await response.text();
          const error = new Error(`API Error: HTTP ${response.status} - ${errorText}`);
          const retryInfo = isRetryableError(error, response);

          if (retryInfo.retryable && retryAttempt < RETRY_CONFIG.maxRetries) {
            const delay = calculateBackoffDelay(retryAttempt);
            console.log(`🔄 ${retryInfo.type} error (${retryInfo.reason}), retrying in ${Math.round(delay)}ms (attempt ${retryAttempt + 1}/${RETRY_CONFIG.maxRetries})`);

            setTimeout(() => {
              generateImageWithGoogleAPI(prompt, model, retryAttempt + 1).then(resolve);
            }, delay);
            return;
          } else {
            console.log(`❌ ${retryInfo.type} error (${retryInfo.reason}), not retryable or max retries exceeded`);
            resolve({
              success: false,
              error: `${retryInfo.type} error: ${retryInfo.reason}`,
              details: errorText,
              retryAttempts: retryAttempt
            });
            return;
          }
        }

        const data = await response.json();
        console.log('Successfully received API response');

        // Debug the response structure
        console.log('Response structure: ' + JSON.stringify(Object.keys(data)));

        // Extract image data from the Imagen API response
        let base64ImageData = null;
        let mimeType = 'image/png'; // Default mime type

        // Extract image data from the response
        console.log('Response data structure:', JSON.stringify(data).substring(0, 200) + '...');

        // Check for images in the response (new Imagen API format)
        if (data.images && data.images.length > 0) {
          base64ImageData = data.images[0].data;
          console.log('Found image data in Imagen API response format');
        }
        // Check for predictions in the response (older Imagen API format)
        else if (data.predictions && data.predictions.length > 0) {
          if (data.predictions[0].bytesBase64Encoded) {
            base64ImageData = data.predictions[0].bytesBase64Encoded;
            console.log('Found image data in predictions[0].bytesBase64Encoded');
          } else if (data.predictions[0].image) {
            base64ImageData = data.predictions[0].image;
            console.log('Found image data in predictions[0].image');
          }
        }
        // Direct image data
        else if (data.image) {
          base64ImageData = data.image;
          console.log('Found direct image data in response');
        }

        if (base64ImageData) {
          // Apply compression to the generated image - CRITICAL FOR FILE SIZE
          console.log('🗜️ CRITICAL: Compressing generated image to prevent 1MB+ files...');
          const compressed = await compressImageData(base64ImageData, CONFIG.TARGET_COMPRESSION_KB);

          if (compressed && !compressed.compressionSkipped) {
            console.log(`✅ COMPRESSION SUCCESS: ${compressed.originalSize}KB → ${compressed.compressedSize}KB`);

            // CRITICAL: Return compressed data as the primary image data
            resolve({
              success: true,
              imageData: compressed.data, // Use compressed data as primary
              mimeType: compressed.mimeType, // Use compressed MIME type (JPEG)
              compressedImageData: compressed.data,
              compressedMimeType: compressed.mimeType,
              originalImageData: base64ImageData, // Keep original for reference
              originalMimeType: mimeType,
              originalSize: compressed.originalSize,
              compressedSize: compressed.compressedSize,
              retryAttempts: retryAttempt
            });
          } else {
            console.error(`❌ COMPRESSION FAILED: ${compressed?.reason || 'Unknown reason'}`);
            console.error('❌ THIS WILL CREATE A LARGE PNG FILE');

            // Even if compression failed, try to force JPEG format to reduce size
            resolve({
              success: true,
              imageData: base64ImageData,
              mimeType: 'image/jpeg', // Force JPEG even if compression failed
              compressedImageData: base64ImageData,
              compressedMimeType: 'image/jpeg',
              originalImageData: base64ImageData,
              originalMimeType: mimeType,
              originalSize: Math.round(base64ImageData.length / 1024),
              compressedSize: Math.round(base64ImageData.length / 1024),
              compressionFailed: true,
              compressionReason: compressed?.reason,
              retryAttempts: retryAttempt
            });
          }
        } else {
          console.log('No image data found in API response');
          console.log('Response structure: ' + JSON.stringify(data, null, 2).substring(0, 500) + '...');
          resolve({
            success: false,
            error: 'No image data received in expected format from API.',
            details: 'The API response did not contain image data in a recognized format.',
            retryAttempts: retryAttempt
          });
        }

      } catch (error) {
        console.log('Error calling Google API: ' + error.message);

        // Handle retryable errors in catch block as well
        const retryInfo = isRetryableError(error, null);

        if (retryInfo.retryable && retryAttempt < RETRY_CONFIG.maxRetries) {
          const delay = calculateBackoffDelay(retryAttempt);
          console.log(`🔄 ${retryInfo.type} error in catch (${retryInfo.reason}), retrying in ${Math.round(delay)}ms (attempt ${retryAttempt + 1}/${RETRY_CONFIG.maxRetries})`);

          setTimeout(() => {
            generateImageWithGoogleAPI(prompt, model, retryAttempt + 1).then(resolve);
          }, delay);
          return;
        } else {
          console.log(`❌ ${retryInfo.type} error in catch (${retryInfo.reason}), not retryable or max retries exceeded`);
          resolve({
            success: false,
            error: `${retryInfo.type} error: ${retryInfo.reason}`,
            details: error.message,
            retryAttempts: retryAttempt
          });
        }
      }
    });
  });
}

// Legacy function - now redirects to Firebase Storage for backward compatibility
async function saveImageToDrive(base64Data, dishName, mimeType) {
  try {
    const storage = StorageFactory.getStorage();
    const folderId = CONFIG.FIREBASE_STORAGE_FOLDER; // Use Firebase storage folder
    return await saveImageToStorage(base64Data, dishName, mimeType, storage, folderId);
  } catch (error) {
    console.log('Error in legacy saveImageToDrive (now Firebase): ' + error.message);
    return null;
  }
}

// Note: Local cache functionality removed - using Firebase Storage as single source of truth





// Test image compression functionality
async function testImageCompression(base64ImageData, targetSizeKB = 100) {
  try {
    console.log(`🧪 Testing compression with target size: ${targetSizeKB}KB`);
    console.log(`📊 Original image size: ${Math.round(base64ImageData.length / 1024)}KB`);

    // Check if we have the dedicated compression test function available from compression.js
    if (typeof window !== 'undefined' && typeof window.testImageCompression === 'function') {
      // Use the dedicated test function from compression.js
      return await window.testImageCompression(base64ImageData, targetSizeKB);
    }

    // Fallback to regular compression function
    const compressionResult = await compressImageData(base64ImageData, targetSizeKB);

    const originalSizeKB = Math.round(base64ImageData.length / 1024);
    const compressedSizeKB = compressionResult.compressedSize || Math.round(compressionResult.data.length / 1024);
    const compressionRatio = Math.round((1 - compressedSizeKB / originalSizeKB) * 100);
    const targetMet = compressedSizeKB <= targetSizeKB;

    // Determine if compression worked properly
    const compressionWorked = !compressionResult.compressionSkipped &&
                             compressionResult.data !== base64ImageData &&
                             compressedSizeKB < originalSizeKB;

    console.log(`🗜️ Compression test results:`);
    console.log(`   Original: ${originalSizeKB}KB`);
    console.log(`   Compressed: ${compressedSizeKB}KB`);
    console.log(`   Reduction: ${compressionRatio}%`);
    console.log(`   Target met: ${targetMet ? 'Yes' : 'No'}`);
    console.log(`   Compression worked: ${compressionWorked ? 'Yes' : 'No'}`);
    console.log(`   Compression skipped: ${compressionResult.compressionSkipped ? 'Yes' : 'No'}`);
    if (compressionResult.reason) {
      console.log(`   Reason: ${compressionResult.reason}`);
    }

    return {
      success: true,
      originalSize: originalSizeKB,
      compressedSize: compressedSizeKB,
      compressionRatio: compressionRatio,
      targetSizeKB: targetSizeKB,
      targetMet: targetMet,
      compressionWorked: compressionWorked,
      compressedData: compressionResult.data,
      compressedMimeType: compressionResult.mimeType,
      quality: compressionResult.quality || 'Unknown',
      compressionSkipped: compressionResult.compressionSkipped || false,
      reason: compressionResult.reason,
      details: {
        originalFormat: 'PNG',
        compressedFormat: compressionResult.mimeType || 'JPEG',
        dimensionsReduced: compressionResult.dimensionsReduced || false,
        qualityReduced: compressionResult.qualityReduced || false
      }
    };
  } catch (error) {
    console.error('❌ Compression test failed:', error);
    return {
      success: false,
      error: 'Compression test failed',
      details: error.message
    };
  }
}

// Test Firebase Storage connection
async function testFirebaseStorageConnection() {
  try {
    console.log('🔥 Testing Firebase Storage connection...');

    const storage = StorageFactory.getStorage();

    // Test 1: Check if we can get an auth token
    const token = await storage.getAuthToken();
    if (!token) {
      return {
        success: false,
        error: 'Failed to get authentication token for Firebase Storage'
      };
    }
    console.log('✅ Authentication token obtained successfully');

    // Test 2: Try to list files in the storage bucket
    try {
      const files = await storage.listFiles('', 'files(id,name)', 5); // List up to 5 files
      console.log(`✅ Successfully listed ${files.length} files from Firebase Storage`);

      return {
        success: true,
        message: 'Firebase Storage connection successful',
        details: {
          authToken: 'Valid',
          bucket: CONFIG.FIREBASE_STORAGE_BUCKET,
          folder: CONFIG.FIREBASE_STORAGE_FOLDER,
          filesFound: files.length,
          testPassed: true
        }
      };
    } catch (listError) {
      console.log('⚠️ Could not list files, but auth token is valid:', listError.message);

      // Even if listing fails, if we got an auth token, the connection is working
      return {
        success: true,
        message: 'Firebase Storage connection successful (auth token valid)',
        details: {
          authToken: 'Valid',
          bucket: CONFIG.FIREBASE_STORAGE_BUCKET,
          folder: CONFIG.FIREBASE_STORAGE_FOLDER,
          filesFound: 'Could not list (may be empty or permission issue)',
          testPassed: true,
          note: 'Auth successful, listing may fail if bucket is empty or has restricted permissions'
        }
      };
    }
  } catch (error) {
    console.error('❌ Firebase Storage test failed:', error);
    return {
      success: false,
      error: 'Firebase Storage connection test failed',
      details: error.message
    };
  }
}

// Note: Cache debugging removed - no local cache in Firebase-only mode

// Test function to check sanitization patterns
function testSanitizationPatterns(dishNames) {
  const results = {};

  dishNames.forEach(dishName => {
    const pattern1 = dishName.replace(/[^\w\s-]/g, '_').trim(); // Old pattern
    const pattern2 = dishName.replace(/[^a-zA-Z0-9_\-. ]/g, '_').trim(); // New pattern

    results[dishName] = {
      original: dishName,
      pattern1: pattern1,
      pattern2: pattern2,
      different: pattern1 !== pattern2
    };
  });

  console.log('🧪 Sanitization Pattern Test:', results);
  return results;
}

// Make debug functions available globally for console access
if (typeof window !== 'undefined') {
  window.testSanitizationPatterns = testSanitizationPatterns;
}

// Search for dish images by name using the storage abstraction
async function searchDishImagesByName(dishName) {
  try {
    const storage = StorageFactory.getStorage();

    // Use consistent sanitization pattern for both search and filename
    const sanitizedFileName = dishName.replace(/[^a-zA-Z0-9_\-. ]/g, '_');
    const escapedSanitizedName = sanitizedFileName.replace(/'/g, "\\'");

    // Create a search query to find files with the exact dish name as filename
    // For Firebase Storage, use simplified query format
    const query = `name = '${escapedSanitizedName}.png' or name = '${escapedSanitizedName}.jpg' or name = '${escapedSanitizedName}.jpeg'`;
    const fields = 'files(id,name,mimeType,description,webViewLink,createdTime)';

    // Search using storage abstraction
    const files = await storage.searchFiles(query, fields);

    return {
      success: true,
      dishName: dishName,
      count: files.length,
      files: files
    };
  } catch (error) {
    console.log('Error in searchDishImagesByName: ' + error.message);
    return {
      success: false,
      dishName: dishName,
      error: 'Error searching for dish images: ' + error.message,
      count: 0,
      files: []
    };
  }
}
