<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page for Dish Image Generator Extension</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .dishItemComponent {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .dish-header-button {
            font-weight: bold;
            font-size: 20px;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .venue-dish-header {
            background-color: #ecf0f1;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
            border-left: 4px solid #3498db;
        }
        .generated-dish-image {
            display: block;
            border: 2px solid #27ae60;
            border-radius: 4px;
        }
        /* Note: Images are now wrapped in a div with margin-top: 10px and margin-right: 8px */
        .ingredients-list {
            color: #666;
            margin-bottom: 15px;
            font-style: italic;
        }
        .dish-details {
            margin-top: 10px;
        }
        .allergens {
            margin-top: 15px;
            font-size: 14px;
            color: #e74c3c;
            background-color: #ffeaa7;
            padding: 8px;
            border-radius: 4px;
        }
        .instructions {
            background-color: #e8f5e8;
            border-left: 4px solid #4CAF50;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Test Page for Dish Image Generator Extension</h1>

    <div class="instructions">
        <h2>Instructions:</h2>
        <ol>
            <li>Install the Dish Image Generator Chrome extension</li>
            <li><strong>Auto-load (Default):</strong> Images should start generating automatically 2 seconds after page load</li>
            <li><strong>Manual mode:</strong> Click extension icon → uncheck "Auto-load" → click "Process Page"</li>
            <li>Watch as images are generated and inserted <strong>after</strong> each venue header (as siblings, not children)</li>
            <li>Note: Dishes with fewer than 4 ingredients will be skipped (like "Simple Toast")</li>
        </ol>
        <p><strong>Expected behavior:</strong> Images will appear on the next row after each blue venue header, not inside it.</p>
        <p><strong>Auto-load feature:</strong> By default, images load automatically when you visit a page. You can disable this in the extension popup.</p>
    </div>

    <h2>Sample Dishes</h2>

    <div class="dishItemComponent">
        <div class="dish-header-button">Spaghetti Carbonara</div>
        <div class="venue-dish-header">
            <strong>Venue Header for Spaghetti Carbonara</strong>
        </div>
        <div class="ingredients-list">pasta, eggs, pancetta, parmesan cheese, black pepper, olive oil</div>
        <div class="dish-details">
            <div>A classic Italian pasta dish with a creamy egg sauce and crispy pancetta.</div>
            <div class="allergens">Contains: eggs, dairy, gluten</div>
        </div>
    </div>

    <div class="dishItemComponent">
        <div class="dish-header-button">Vegetable Curry</div>
        <div class="venue-dish-header">
            <strong>Venue Header for Vegetable Curry</strong>
        </div>
        <div class="ingredients-list">potatoes, carrots, peas, onions, garlic, curry powder, coconut milk, vegetable broth</div>
        <div class="dish-details">
            <div>A flavorful vegetarian curry with mixed vegetables in a rich coconut sauce.</div>
            <div class="allergens">Contains: coconut</div>
        </div>
    </div>

    <div class="dishItemComponent">
        <div class="dish-header-button">Simple Toast</div>
        <div class="venue-dish-header">
            <strong>Venue Header for Simple Toast (Should be skipped - only 3 ingredients)</strong>
        </div>
        <div class="ingredients-list">bread, butter, jam</div>
        <div class="dish-details">
            <div>Simple toast with butter and jam. This should be skipped due to insufficient ingredients.</div>
            <div class="allergens">Contains: gluten, dairy</div>
        </div>
    </div>

    <div class="dishItemComponent">
        <div class="dish-header-button">Grilled Salmon</div>
        <div class="venue-dish-header">
            <strong>Venue Header for Grilled Salmon</strong>
        </div>
        <div class="ingredients-list">salmon fillet, lemon, herbs, olive oil, garlic, salt, pepper</div>
        <div class="dish-details">
            <div>Fresh Atlantic salmon grilled to perfection with herbs and lemon.</div>
            <div class="allergens">Contains: fish</div>
        </div>
    </div>

    <div class="dishItemComponent">
        <div class="dish-header-button">Chocolate Cake</div>
        <div class="venue-dish-header">
            <strong>Venue Header for Chocolate Cake</strong>
        </div>
        <div class="ingredients-list">flour, cocoa powder, sugar, eggs, butter, vanilla, baking powder</div>
        <div class="dish-details">
            <div>Rich and moist chocolate cake with a decadent chocolate frosting.</div>
            <div class="allergens">Contains: gluten, eggs, dairy</div>
        </div>
    </div>

    <div class="dishItemComponent">
        <div class="dish-header-button">Caesar Salad</div>
        <div class="venue-dish-header">
            <strong>Venue Header for Caesar Salad</strong>
        </div>
        <div class="ingredients-list">romaine lettuce, parmesan cheese, croutons, caesar dressing, anchovies</div>
        <div class="dish-details">
            <div>Crisp romaine lettuce with classic Caesar dressing and fresh parmesan.</div>
            <div class="allergens">Contains: dairy, fish, gluten</div>
        </div>
    </div>

    <div class="dishItemComponent">
        <div class="dish-header-button">Beef Tacos</div>
        <div class="venue-dish-header">
            <strong>Venue Header for Beef Tacos</strong>
        </div>
        <div class="ingredients-list">ground beef, corn tortillas, onions, cilantro, lime, cheese, salsa</div>
        <div class="dish-details">
            <div>Authentic Mexican tacos with seasoned ground beef and fresh toppings.</div>
            <div class="allergens">Contains: dairy</div>
        </div>
    </div>

    <script>
        // Add some interactivity to show the extension is working
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded with', document.querySelectorAll('.dishItemComponent').length, 'dish elements');

            // Add a visual indicator when images are being processed
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        mutation.addedNodes.forEach(function(node) {
                            if (node.nodeType === 1 && node.classList.contains('generated-dish-image')) {
                                console.log('Image generated for dish!');
                                // Add a subtle animation when image appears
                                node.style.opacity = '0';
                                node.style.transition = 'opacity 0.5s ease-in';
                                setTimeout(() => {
                                    node.style.opacity = '1';
                                }, 100);
                            }
                        });
                    }
                });
            });

            // Observe all dish components for changes
            document.querySelectorAll('.dishItemComponent').forEach(function(dish) {
                observer.observe(dish, { childList: true, subtree: true });
            });
        });
    </script>
</body>
</html>
