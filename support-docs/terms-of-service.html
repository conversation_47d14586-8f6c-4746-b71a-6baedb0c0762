<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Terms of Service - Eat (with images)</title>
    <style>
        body {
            font-family: '<PERSON><PERSON>', <PERSON>l, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1 { color: #1a73e8; }
        h2 { color: #1a73e8; margin-top: 30px; }
        .last-updated { color: #666; font-style: italic; }
    </style>
</head>
<body>
    <h1>Terms of Service</h1>
    <p class="last-updated">Last updated: [DATE]</p>

    <h2>1. Acceptance of Terms</h2>
    <p>By installing and using the Eat (with images) Chrome extension ("the Extension"), you agree to be bound by these Terms of Service ("Terms"). If you do not agree to these Terms, do not use the Extension.</p>

    <h2>2. Description of Service</h2>
    <p>Eat (with images) is a Chrome extension that generates and caches images of dishes using Google's Generative AI technology. The Extension is designed to work exclusively with eat.googleplex.com and is intended for use by Google employees.</p>

    <h2>3. Permitted Use</h2>
    <p>You may use the Extension solely for:</p>
    <ul>
        <li>Viewing AI-generated images of dishes on eat.googleplex.com</li>
        <li>Personal use in connection with Google's internal dining services</li>
        <li>Legitimate business purposes related to your employment at Google</li>
    </ul>

    <h2>4. Restrictions</h2>
    <p>You agree not to:</p>
    <ul>
        <li>Use the Extension for any unlawful purpose</li>
        <li>Attempt to reverse engineer or modify the Extension</li>
        <li>Use the Extension outside of eat.googleplex.com</li>
        <li>Share generated images for commercial purposes</li>
    </ul>

    <h2>5. Privacy and Data</h2>
    <p>Your use of the Extension is subject to our Privacy Policy. The Extension requires access to Google Drive for image caching and Google Cloud Platform for AI image generation.</p>

    <h2>6. Google Services Integration</h2>
    <p>The Extension integrates with Google services including Google Drive and Google Cloud Platform. Your use of these services is subject to Google's Terms of Service.</p>

    <h2>7. Intellectual Property</h2>
    <p>The Extension and all generated images are provided for internal Google use only. Generated images are created using Google's AI technology and are subject to Google's intellectual property policies.</p>

    <h2>8. Disclaimer of Warranties</h2>
    <p>The Extension is provided "as is" without warranties of any kind. We do not guarantee the accuracy, completeness, or reliability of generated images.</p>

    <h2>9. Limitation of Liability</h2>
    <p>In no event shall we be liable for any indirect, incidental, special, or consequential damages arising from your use of the Extension.</p>

    <h2>10. Termination</h2>
    <p>We may terminate or suspend your access to the Extension at any time without notice for any reason, including violation of these Terms.</p>

    <h2>11. Changes to Terms</h2>
    <p>We reserve the right to modify these Terms at any time. Continued use of the Extension after changes constitutes acceptance of the new Terms.</p>

    <h2>12. Contact Information</h2>
    <p>For questions about these Terms, please contact: <EMAIL></p>

    <h2>13. Governing Law</h2>
    <p>These Terms are governed by the laws of California, United States.</p>
</body>
</html>
