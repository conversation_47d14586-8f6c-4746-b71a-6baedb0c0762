<html ng-app="food" lang="en"><head>
  
<title>Eat</title>
  <!--<base href="/">--><base href=".">
  
  <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="theme-color" content="#33691e">
  <link href="https://eat.googleplex.com/eat/assets/favicon.png" type="image/png" rel="icon">
  <link href="./Eat_files/css" rel="stylesheet">
  <link href="./Eat_files/icon" rel="stylesheet">
  <link rel="stylesheet" href="./Eat_files/styles.css">
  <style>@charset "UTF-8";

[ng\:cloak], [ng-cloak], [data-ng-cloak], [x-ng-cloak],
.ng-cloak, .x-ng-cloak,
.ng-hide:not(.ng-hide-animate) {
  display: none !important;
}

ng\:form {
  display: block;
}

.ng-animate-shim {
  visibility:hidden;
}

.ng-anchor {
  position:absolute;
}
</style>
  <style>
    /* Custom styles for loading indicator on Eat page */
    .venue-card-container.loading {
      background-color: transparent !important;
      border: none !important;
      display: flex; /* Use flexbox to ensure centering */
      justify-content: center;
      align-items: center;
      min-height: 200px; /* Ensure enough height for centering */
    }

    .venue-card-container.loading .venue-image-wrapper,
    .venue-card-container.loading .venue-card-header,
    .venue-card-container.loading .dishMenuComponent,
    .venue-card-container.loading .status-and-more {
        display: none !important; /* Hide other elements when loading */
    }

    md-progress-circular.venue-loader {
      background-color: transparent !important;
      border: none !important;
    }
  </style>
  <script async="" src="./Eat_files/js" ng-non-bindable=""></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    window.gtag = function () {
      dataLayer.push(arguments);
    }
    ;
    window.gtag('js', new Date());
    window.gtag('config', 'G-L48SM5E6FM');
  </script>
</head>
<body layout="column" class="food-body foodBodyComponent foodBodyComponent__container layout-column disable-focus-highlight" global-shortcuts="" data-llm4sre-ubi-main-called_35.6="true">

<!----><ng-view flex="" layout="column" class="layout-column flex"><div class="mainEatComponent mainEatComponent__container layout-xs-column layout-row flex" flex="" layout="" layout-xs="column">
  <div flex="" layout="column" class="layout-column flex">
    
    
    <div class="mainEatComponent__main-panel layout-row flex" flex="" id="main-panel" layout="">
      <!----><!---->
      
      <md-content flex="" aria-labelledby="menus-title" class="reader-mode-background mainEatComponent__cards-container _md flex" id="cards-container" role="region" ng-class="{'share-mode': ctrl.venues.isShareView}">
        <div class="cards-inner-container layout-align-center-stretch layout-column" layout="column" layout-align="center stretch" id="cards-inner-container" ng-class="{'layout-fill': ctrl.loading || !ctrl.venues.displayedVenues.length}" >
          <!----><!---->
          <!----><md-divider ng-if="!ctrl.loading &amp;&amp; ctrl.venues.displayedVenues.length" class="" ></md-divider><!---->
          <!---->
          <!---->
          <!---->
          <!----><div layout="column" layout-align="start center" ng-class="{'layout-fill': ctrl.loading || !ctrl.venues.displayedVenues.length}" ng-if="!ctrl.updatingView" class="layout-align-start-center layout-column" >
            <food-office-announcements announcements="ctrl.officeAnnouncements"><div class="officeAnnouncementComponent">
  <!---->
</div>
</food-office-announcements>
            <div flex="" layout="" layout-align="center center" class="layout-align-center-center layout-row flex">
              <!---->
              <!---->
              <!---->
            </div>
            <food-venue-cards-list layout="" class="venue-cards-component layout-row" role="tabpanel" tabindex="-1" is-small-venue-cards-size="ctrl.isSmallVenueCardsSize()" toggle-venue-favorite="ctrl.toggleVenueFavorite(id)"><div class="venueCardList__container">
  
  <!----><div class="compact-venue-cards" ng-class="{'widened': ctrl.settingsService.isVenueListCollapsed()}" ng-if="ctrl.isSmallVenueCardsSize()">
    <!----><md-card layout="" flex="" class="venue-card md-card _md layout-row flex" id="4391330" ng-init="venue = ctrl.venues.getVenue(venueId)" ng-repeat="venueId in ctrl.venues.displayedVenues" ng-style="ctrl.getCardStyle(venueId)" style="grid-row-end: span 129;">
      <div flex="" class="venue-card-container layout-column flex" layout="column" ng-class="{'loading': ctrl.venues.isLoadingMenu(venueId)}" >
        <div layout="" layout-align="center center" class="print-hidden cards-actions-wrapper layout-align-center-center layout-row">
          <!---->
          <!----><a class="md-icon-button location-button md-button md-ink-ripple" ng-transclude="" aria-label="'Open map view for Park Cafe'" md-ink-ripple="#fff" target="_blank" ng-href="https://campusmaps.googleplex.com/?q=US-SVL-CRSM1265-1-1B7" ng-if="venue | formatLocation: ctrl.venues.getSelectedMealDate(venueId)" href="https://campusmaps.googleplex.com/?q=US-SVL-CRSM1265-1-1B7">
            
            <md-icon class="md-icon material-icons" aria-hidden="true" role="img">place</md-icon>
          </a><!---->
          <!----><button class="md-icon-button share-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="'Get link to Park Cafe's menu'" md-ink-ripple="#fff" ng-click="ctrl.openShareDialog(venueId)" ng-if="!ctrl.venues.isShareView">
            
            <md-icon class="md-icon material-icons" aria-hidden="true" role="img">share</md-icon>
          </button><!---->
          <md-progress-circular class="venue-loader md-mode-indeterminate ng-hide" md-diameter="24" md-mode="indeterminate" ng-show="ctrl.venueLoadingStatuses[venueId]" aria-valuemin="0" aria-valuemax="100" role="progressbar" aria-hidden="true" style="width: 24px; height: 24px;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" style="width: 24px; height: 24px; transform-origin: 12px 12px 12px;"><path fill="none" stroke-width="2.4000000000000004" stroke-linecap="square" d="M12,1.2000000000000002A10.8,10.8 0 1 1 1.2000000000000002,12" stroke-dasharray="50.89380098815465" stroke-dashoffset="84.49965808376052" transform="rotate(-180 12 12)"></path></svg></md-progress-circular>
          <button class="md-icon-button favorite-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="Add Park Cafe to favorites" aria-pressed="true" aria-description="Park Cafe is in favorites" id="favorite-button-Park Cafe" md-ink-ripple="#fff" ng-class="{'loading-statuses': ctrl.venueLoadingStatuses[venueId]}" ng-click="ctrl.toggleVenueFavorite(venueId)">
            
            <md-icon class="md-icon material-icons" aria-hidden="true" role="img">
              favorite
            </md-icon>
          </button>
          <span flex="" class="flex"></span>
          <!----><button class="card-dismiss-button md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="Dismiss Park Cafe card" md-ink-ripple="#fff" ng-click="ctrl.dismissVenueCard(venueId)" ng-if="!ctrl.venues.isShareView">
            
            <md-icon class="md-icon material-icons" aria-hidden="true" role="img">close</md-icon>
          </button><!---->
        </div>
        <div class="venue-image-wrapper">
          <!----><div class="venue-image" style="background-image: url(https://lh3.googleusercontent.com/sZ9lnJz3ghc8oRBxYlafUvHLAtb67CHgbcpMVr9PXlpGmY8gQxOLJMykG9SpwqHFZddd60GC8GPUppgrJg1rwGy_6GrmauJ3EEs)" ng-if="venue.image[0]">
          </div><!---->
        </div>
        <div layout="" class="print-center venue-card-header layout-align-start-start layout-row" layout-align="start start">
          <div flex="" layout="column" class="layout-column flex">
            <h3 class="md-title" id="venue-name-4391330">Park Cafe</h3>




            <food-venue-card-details class="printable card-details venueDetailsComponent__printable" schedule="ctrl.venues.getSchedule(venueId)" selected-meal-date="ctrl.venues.getSelectedMealDate(venueId)" venue="venue"><div class="venueDetailsComponent">
  <div class="schedule-container venueDetailsComponent__container layout-column flex" flex="" layout="column">
  <!----><span class="description" ng-if="ctrl.venue.description">
    With stations serving authentic California fare, delectable hearth offerings, tasty salads and make-your-own deli, this cafe makes it a walk in the park to find plated and custom eats that are sure to satisfy taste buds.
  </span><!---->
    <!----><a aria-label="SVL CRSM1265, Floor 1
1265 Crossman Avenue, Sunnyvale, CA 94089, US location" class="address md-ink-ripple" md-ink-ripple="" ng-href="https://campusmaps.googleplex.com/?q=US-SVL-CRSM1265-1-1B7" ng-if="ctrl.venue | formatLocation: ctrl.selectedMealDate" target="_blank" href="https://campusmaps.googleplex.com/?q=US-SVL-CRSM1265-1-1B7">
      <div layout="" class="layout-row">
        <div>
          <md-icon aria-hidden="true" class="venueDetailsComponent__icon material-icons" role="img">place</md-icon>
        </div>
        <div class="location-text">SVL CRSM1265, Floor 1
1265 Crossman Avenue, Sunnyvale, CA 94089, US</div>
      </div>
    </a><!---->
    <div class="schedule layout-row" layout="">
      <div>
        <md-icon aria-hidden="true" class="venueDetailsComponent__icon material-icons" role="img">schedule</md-icon>
      </div>
      <div>
        <div class="schedule-date">today, May 5</div>
        <!---->
        <div layout="column" class="layout-column">
          <!----><div ng-repeat="period in ctrl.dailySchedule">
            <div ng-class="{'green': ctrl.isServing(period) &amp;&amp; !period.isClosedForSpecialHours}">
              <span>Breakfast:</span>
              <!---->
              <!----><span ng-if="!period.isClosedForSpecialHours">7:30 am - 10:00 am</span><!---->
            </div>
          </div><!----><div ng-repeat="period in ctrl.dailySchedule">
            <div ng-class="{'green': ctrl.isServing(period) &amp;&amp; !period.isClosedForSpecialHours}">
              <span>Lunch:</span>
              <!---->
              <!----><span ng-if="!period.isClosedForSpecialHours">11:30 am - 1:30 pm</span><!---->
            </div>
          </div><!----><div ng-repeat="period in ctrl.dailySchedule">
            <div ng-class="{'green': ctrl.isServing(period) &amp;&amp; !period.isClosedForSpecialHours}">
              <span>Dinner:</span>
              <!---->
              <!----><span ng-if="!period.isClosedForSpecialHours">6:30 pm - 8:00 pm</span><!---->
            </div>
          </div><!---->
        </div>
      </div>
    </div>
    <div class="schedule-button">
      <button class="md-primary md-button md-ink-ripple" type="button" ng-transclude="" ng-click="ctrl.openWeeklySchedule($event)">
        View full schedule
      </button>
    </div>
  </div>

</div>
</food-venue-card-details>
          </div>
          <div layout="column" layout-align="start end" class="status-and-more layout-align-start-end layout-column">
            <!----><food-status class="food-status layout-align-end-end layout-row" layout="" layout-align="end end" status="ctrl.venues.getStatus(venueId)" ng-if="ctrl.venues.getStatus(venueId)"><div class="foodStatusComponent">
  <!----><div class="foodStatusComponent__container layout-row status-red" layout="row" ng-class="ctrl.getCssClassForStatus()" ng-if="ctrl.status">
    <div class="foodStatusComponent__container--content layout-row" layout="">
      <!----><span ng-repeat="statusPart in ctrl.status.statusTextParts" ng-class="{'bold': $index == ctrl.status.highlightAtIndex}">
        Opens&nbsp;
      </span><!----><span ng-repeat="statusPart in ctrl.status.statusTextParts" ng-class="{'bold': $index == ctrl.status.highlightAtIndex}">
        at&nbsp;
      </span><!----><span ng-repeat="statusPart in ctrl.status.statusTextParts" ng-class="{'bold': $index == ctrl.status.highlightAtIndex}" class="bold">
        6:30 PM&nbsp;
      </span><!---->
    </div>
  </div><!---->
</div>
</food-status><!---->
            <button class="print-hidden details-toggle md-button md-ink-ripple" type="button" ng-transclude="" aria-describedby="venue-name-4391330" aria-expanded="false" role="button" ng-click="expanded = !expanded; ctrl.toggleVenueDetails(expanded)">
              More info
              <md-icon aria-hidden="true" class="md-icon material-icons" role="img">expand_more
              </md-icon>
            </button>
          </div>
        </div>
        <!---->
        <md-divider></md-divider>
        <!---->
        <!---->
        <!----><food-venue-card-menu class="dishMenuComponent layout-row flex" flex="" layout="" ng-if="ctrl.venues.getMenu(venueId)" menu="ctrl.venues.getMenu(venueId)" venue-id="venueId" ><div class="dishMenuComponent">
  <div class="venue-station dishMenuComponent__container layout-column flex" flex="" layout="column">
    <!---->
    <!----><div class="venue-station-item" ng-repeat="station in ctrl.menu.stations" >
      <h4 aria-description="Park Cafe Counter is expanded" aria-expanded="true" aria-label="Park Cafe Counter" class="station-header layout-align-start-center layout-row" layout="" layout-align="start center" ng-click="ctrl.toggleCollapsedStation($event, station.stationId)" role="heading" tabindex="0">
        <span flex="" class="flex">Park Cafe Counter</span>
        <!---->
        <md-icon aria-hidden="true" class="print-hidden material-icons" role="img">
          expand_less
        </md-icon>
      </h4>
      <!----><!----><div ng-if="!ctrl.isStationCollapsed(station.stationId)" ng-repeat="dish in station.dishList.dishes">
        <food-dish-item dish="dish" venue-id="ctrl.venueId"><div class="dishItemComponent">
  <div aria-expanded="true" class="dish dishItemComponent__container expandable" ng-class="(ctrl.containsAvoidance() ? 'avoidance' : '')" role="presentation">
    <div class="venue-dish-header layout-row" layout="" ng-click="ctrl.toggleDish($event)" role="button" tabindex="0">
      <div class="venue-dish-title layout-row flex" flex="" layout="">
      <!---->
        <h5 aria-label="Vegetarian Cassoulet" class="dish-header-button flex" dir="ltr" flex="">
          Vegetarian Cassoulet
        </h5>
      </div>
      <!----><div class="diet-tags" ng-if="ctrl.showDiet()">
        <h6 class="food-invisible-text">Diet</h6>
        <!----><div class="diet-item Vegan layout-align-end-center layout-row" layout="" layout-align="end center" ng-repeat="tag in ctrl.dish.dietTags">
          Vegan
        </div><!---->
      </div><!---->
      <md-icon aria-hidden="true" class="dish-expand-button-icon material-icons" role="img">
        expand_less
      </md-icon>
    </div>
    <!----><div class="dish-details" ng-if="ctrl.isDishExpanded">
      <!----><div class="highlighting-reason" ng-if="ctrl.shouldShowRationales()">
        <div class="allergy-container">
        <!---->
          <!---->
          <!---->
        </div>
      </div><!---->
      <!----><div class="allergens selectable layout-align-start-start layout-row" layout="" layout-align="start start" ng-if="ctrl.shouldShowAllergens()">
        <div>
          <h6 class="dishItemComponent__allergens-title">Contains:&nbsp;</h6>
          Tree Nuts (Coconut), Wheat
        </div>
      </div><!---->
      <div class="ingredients selectable layout-row" dir="ltr" layout="">
        <div class="ingredients-expand-button">
          <h6 class="dishItemComponent__ingredients-title">Ingredients:&nbsp;</h6>
        </div>
        <span class="ingredients-list" ng-class="{'separated': ctrl.dataStore.hasUserDeskCountryMigrated}">
          Cannellini Bean, Plant-Based Sweet Italian Sausage, Fennel, Carrot, Onion, Tomato Paste, Canola Oil, Vegetable Broth, Garlic, Apple Cider Vinegar, Dried Thyme, Bay Leaf, Kosher Salt, Ground Black Pepper, Toasted Panko Breadcrumbs (Panko, Extra Virgin Olive Oil, Oregano, Kosher Salt, Ground Black Pepper)
        </span>
      </div>
      <!----><div class="feedback-actions layout-row" layout="" ng-if="ctrl.shouldShowFeedbackActions()">
        <div class="feedback-action feedback-action-like layout-align-center-center layout-row" layout="" layout-align="center center">
          <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="0 likes" aria-pressed="false" ng-click="$event.stopPropagation(); ctrl.toggleLike($event)" tabindex="0">
            <md-icon aria-hidden="true" class="feedback-action__icon material-icons" ng-class="{active: ctrl.dish.isLiked}" role="img">
              thumb_up_off_alt
            </md-icon>
            
          </button>
          <!----><span aria-hidden="true" class="feedback-count" ng-if="!ctrl.isLoading">
          
        </span><!---->
          <!---->
        </div>
        <div class="feedback-action feedback-action-comment layout-align-center-center layout-row" layout="" layout-align="center center">
          <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="0 comments" ng-click="$event.stopPropagation(); ctrl.openDishCommentsDialog($event)" tabindex="0">
            <md-icon aria-hidden="true" class="feedback-action__icon material-icons" role="img">chat_bubble_outline</md-icon>
            
          </button>
          <span aria-hidden="true" class="feedback-count">
          
        </span>
        </div>
      </div><!---->
    </div><!---->
  </div>
  <md-divider class="venue-dish-divider"></md-divider>
</div>
</food-dish-item>
      </div><!----><!----><!----><div ng-if="!ctrl.isStationCollapsed(station.stationId)" ng-repeat="dish in station.dishList.dishes">
        <food-dish-item dish="dish" venue-id="ctrl.venueId"><div class="dishItemComponent">
  <div aria-expanded="true" class="dish dishItemComponent__container expandable" ng-class="(ctrl.containsAvoidance() ? 'avoidance' : '')" role="presentation">
    <div class="venue-dish-header layout-row" layout="" ng-click="ctrl.toggleDish($event)" role="button" tabindex="0">
      <div class="venue-dish-title layout-row flex" flex="" layout="">
      <!---->
        <h5 aria-label="Roasted Asparagus with Tarragon Vinaigrette" class="dish-header-button flex" dir="ltr" flex="">
          Roasted Asparagus with Tarragon Vinaigrette
        </h5>
      </div>
      <!----><div class="diet-tags" ng-if="ctrl.showDiet()">
        <h6 class="food-invisible-text">Diet</h6>
        <!----><div class="diet-item Vegan layout-align-end-center layout-row" layout="" layout-align="end center" ng-repeat="tag in ctrl.dish.dietTags">
          Vegan
        </div><!---->
      </div><!---->
      <md-icon aria-hidden="true" class="dish-expand-button-icon material-icons" role="img">
        expand_less
      </md-icon>
    </div>
    <!----><div class="dish-details" ng-if="ctrl.isDishExpanded">
      <!----><div class="highlighting-reason" ng-if="ctrl.shouldShowRationales()">
        <div class="allergy-container">
        <!---->
          <!---->
          <!---->
        </div>
      </div><!---->
      <!----><div class="allergens selectable layout-align-start-start layout-row" layout="" layout-align="start start" ng-if="ctrl.shouldShowAllergens()">
        <div>
          <h6 class="dishItemComponent__allergens-title">Contains:&nbsp;</h6>
          Sulfite
        </div>
      </div><!---->
      <div class="ingredients selectable layout-row" dir="ltr" layout="">
        <div class="ingredients-expand-button">
          <h6 class="dishItemComponent__ingredients-title">Ingredients:&nbsp;</h6>
        </div>
        <span class="ingredients-list" ng-class="{'separated': ctrl.dataStore.hasUserDeskCountryMigrated}">
          Roasted Asparagus (Asparagus, Rice Bran Oil, Kosher Salt), Shallot And Tarragon Dressing (Extra Virgin Olive Oil, Pear Vinegar, Shallot, Dijon Mustard, Granulated Sugar, Tarragon, Kosher Salt)
        </span>
      </div>
      <!----><div class="feedback-actions layout-row" layout="" ng-if="ctrl.shouldShowFeedbackActions()">
        <div class="feedback-action feedback-action-like layout-align-center-center layout-row" layout="" layout-align="center center">
          <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="0 likes" aria-pressed="false" ng-click="$event.stopPropagation(); ctrl.toggleLike($event)" tabindex="0">
            <md-icon aria-hidden="true" class="feedback-action__icon material-icons" ng-class="{active: ctrl.dish.isLiked}" role="img">
              thumb_up_off_alt
            </md-icon>
            
          </button>
          <!----><span aria-hidden="true" class="feedback-count" ng-if="!ctrl.isLoading">
          
        </span><!---->
          <!---->
        </div>
        <div class="feedback-action feedback-action-comment layout-align-center-center layout-row" layout="" layout-align="center center">
          <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="0 comments" ng-click="$event.stopPropagation(); ctrl.openDishCommentsDialog($event)" tabindex="0">
            <md-icon aria-hidden="true" class="feedback-action__icon material-icons" role="img">chat_bubble_outline</md-icon>
            
          </button>
          <span aria-hidden="true" class="feedback-count">
          
        </span>
        </div>
      </div><!---->
    </div><!---->
  </div>
  <md-divider class="venue-dish-divider"></md-divider>
</div>
</food-dish-item>
      </div><!----><!----><!----><div ng-if="!ctrl.isStationCollapsed(station.stationId)" ng-repeat="dish in station.dishList.dishes">
        <food-dish-item dish="dish" venue-id="ctrl.venueId"><div class="dishItemComponent">
  <div aria-expanded="true" class="dish dishItemComponent__container expandable" ng-class="(ctrl.containsAvoidance() ? 'avoidance' : '')" role="presentation">
    <div class="venue-dish-header layout-row" layout="" ng-click="ctrl.toggleDish($event)" role="button" tabindex="0">
      <div class="venue-dish-title layout-row flex" flex="" layout="">
      <!---->
        <h5 aria-label="Le Puy Lentils with Fennel" class="dish-header-button flex" dir="ltr" flex="">
          Le Puy Lentils with Fennel
        </h5>
      </div>
      <!----><div class="diet-tags" ng-if="ctrl.showDiet()">
        <h6 class="food-invisible-text">Diet</h6>
        <!----><div class="diet-item Vegan layout-align-end-center layout-row" layout="" layout-align="end center" ng-repeat="tag in ctrl.dish.dietTags">
          Vegan
        </div><!---->
      </div><!---->
      <md-icon aria-hidden="true" class="dish-expand-button-icon material-icons" role="img">
        expand_less
      </md-icon>
    </div>
    <!----><div class="dish-details" ng-if="ctrl.isDishExpanded">
      <!----><div class="highlighting-reason" ng-if="ctrl.shouldShowRationales()">
        <div class="allergy-container">
        <!---->
          <!---->
          <!---->
        </div>
      </div><!---->
      <!---->
      <div class="ingredients selectable layout-row" dir="ltr" layout="">
        <div class="ingredients-expand-button">
          <h6 class="dishItemComponent__ingredients-title">Ingredients:&nbsp;</h6>
        </div>
        <span class="ingredients-list" ng-class="{'separated': ctrl.dataStore.hasUserDeskCountryMigrated}">
          Lentil, Slow Cooked Fennel (Fennel, Extra Virgin Olive Oil, Kosher Salt), Yellow Onion, Carrot, Celery, Garlic, Extra Virgin Olive Oil, Kosher Salt, Ground Black Pepper, Lemon Juice, Parsley
        </span>
      </div>
      <!----><div class="feedback-actions layout-row" layout="" ng-if="ctrl.shouldShowFeedbackActions()">
        <div class="feedback-action feedback-action-like layout-align-center-center layout-row" layout="" layout-align="center center">
          <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="0 likes" aria-pressed="false" ng-click="$event.stopPropagation(); ctrl.toggleLike($event)" tabindex="0">
            <md-icon aria-hidden="true" class="feedback-action__icon material-icons" ng-class="{active: ctrl.dish.isLiked}" role="img">
              thumb_up_off_alt
            </md-icon>
            
          </button>
          <!----><span aria-hidden="true" class="feedback-count" ng-if="!ctrl.isLoading">
          
        </span><!---->
          <!---->
        </div>
        <div class="feedback-action feedback-action-comment layout-align-center-center layout-row" layout="" layout-align="center center">
          <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="0 comments" ng-click="$event.stopPropagation(); ctrl.openDishCommentsDialog($event)" tabindex="0">
            <md-icon aria-hidden="true" class="feedback-action__icon material-icons" role="img">chat_bubble_outline</md-icon>
            
          </button>
          <span aria-hidden="true" class="feedback-count">
          
        </span>
        </div>
      </div><!---->
    </div><!---->
  </div>
  <md-divider class="venue-dish-divider"></md-divider>
</div>
</food-dish-item>
      </div><!----><!---->
      <!---->
    </div><!---->
    <!----><div class="discouraged-dishes-header print-hidden layout-align-start-center layout-row" layout="" layout-align="none center" ng-if="ctrl.menu.hasHiddenDishes()" >
      <h4 ng-class="{'hide-show-all-button': ctrl.showDiscouragedDishes || !ctrl.showDiscouragedDishes}" class="hide-show-all-button">
        1 dishes filtered out (from Eater Profile)
      </h4>
      <span flex="" class="flex"></span>
      <button class="md-button md-ink-ripple" type="button" ng-transclude="" aria-expanded="false" ng-click="ctrl.showDiscouragedDishes = !ctrl.showDiscouragedDishes">
        Show all
      </button>
    </div><!---->
    <!----><!----><!---->
  </div>
</div>
</food-venue-card-menu><!---->
      </div>
      <!---->
    </md-card><!----><md-card layout="" flex="" class="venue-card md-card _md layout-row flex" id="4391164" ng-init="venue = ctrl.venues.getVenue(venueId)" ng-repeat="venueId in ctrl.venues.displayedVenues" ng-style="ctrl.getCardStyle(venueId)" style="grid-row-end: span 156;">
      <div flex="" class="venue-card-container layout-column flex" layout="column" ng-class="{'loading': ctrl.venues.isLoadingMenu(venueId)}" >
        <div layout="" layout-align="center center" class="print-hidden cards-actions-wrapper layout-align-center-center layout-row">
          <!---->
          <!----><a class="md-icon-button location-button md-button md-ink-ripple" ng-transclude="" aria-label="'Open map view for The Studio'" md-ink-ripple="#fff" target="_blank" ng-href="https://campusmaps.googleplex.com/?q=US-SVL-CRSM1265-1-1D0" ng-if="venue | formatLocation: ctrl.venues.getSelectedMealDate(venueId)" href="https://campusmaps.googleplex.com/?q=US-SVL-CRSM1265-1-1D0">
            
            <md-icon class="md-icon material-icons" aria-hidden="true" role="img">place</md-icon>
          </a><!---->
          <!----><button class="md-icon-button share-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="'Get link to The Studio's menu'" md-ink-ripple="#fff" ng-click="ctrl.openShareDialog(venueId)" ng-if="!ctrl.venues.isShareView">
            
            <md-icon class="md-icon material-icons" aria-hidden="true" role="img">share</md-icon>
          </button><!---->
          <md-progress-circular class="venue-loader md-mode-indeterminate ng-hide" md-diameter="24" md-mode="indeterminate" ng-show="ctrl.venueLoadingStatuses[venueId]" aria-valuemin="0" aria-valuemax="100" role="progressbar" aria-hidden="true" style="width: 24px; height: 24px;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" style="width: 24px; height: 24px; transform-origin: 12px 12px 12px;"><path fill="none" stroke-width="2.4000000000000004" stroke-linecap="square" d="M12,1.2000000000000002A10.8,10.8 0 1 1 1.2000000000000002,12" stroke-dasharray="50.89380098815465" stroke-dashoffset="85.09275224371301" transform="rotate(-180 12 12)"></path></svg></md-progress-circular>
          <button class="md-icon-button favorite-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="Add The Studio to favorites" aria-pressed="true" aria-description="The Studio is in favorites" id="favorite-button-The Studio" md-ink-ripple="#fff" ng-class="{'loading-statuses': ctrl.venueLoadingStatuses[venueId]}" ng-click="ctrl.toggleVenueFavorite(venueId)">
            
            <md-icon class="md-icon material-icons" aria-hidden="true" role="img">
              favorite
            </md-icon>
          </button>
          <span flex="" class="flex"></span>
          <!----><button class="card-dismiss-button md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="Dismiss The Studio card" md-ink-ripple="#fff" ng-click="ctrl.dismissVenueCard(venueId)" ng-if="!ctrl.venues.isShareView">
            
            <md-icon class="md-icon material-icons" aria-hidden="true" role="img">close</md-icon>
          </button><!---->
        </div>
        <div class="venue-image-wrapper">
          <!----><div class="venue-image" style="background-image: url(https://lh3.googleusercontent.com/WJSR4eM69IhU1BZGGFsOm0I5WUvEXCXBfuxFZCSp99cJflyqFjx1qm8b2nvP4DpebUpebjQ6wu4-qab8EjxQnr076Y2G-1HocgE)" ng-if="venue.image[0]">
          </div><!---->
        </div>
        <div layout="" class="print-center venue-card-header layout-align-start-start layout-row" layout-align="start start">
          <div flex="" layout="column" class="layout-column flex">
            <h3 class="md-title" id="venue-name-4391164">The Studio</h3>




            <food-venue-card-details class="printable card-details venueDetailsComponent__printable" schedule="ctrl.venues.getSchedule(venueId)" selected-meal-date="ctrl.venues.getSelectedMealDate(venueId)" venue="venue"><div class="venueDetailsComponent">
  <div class="schedule-container venueDetailsComponent__container layout-column flex" flex="" layout="column">
  <!----><span class="description" ng-if="ctrl.venue.description">
    Be ready for a parade of zesty global flavors from South and East Asia, the Middle East and the Mediterranean region. Venture to Kaffeeklasch for delicious drinks.
  </span><!---->
    <!----><a aria-label="SVL CRSM1265, Floor 1
1265 Crossman Avenue, Sunnyvale, CA 94089, US location" class="address md-ink-ripple" md-ink-ripple="" ng-href="https://campusmaps.googleplex.com/?q=US-SVL-CRSM1265-1-1D0" ng-if="ctrl.venue | formatLocation: ctrl.selectedMealDate" target="_blank" href="https://campusmaps.googleplex.com/?q=US-SVL-CRSM1265-1-1D0">
      <div layout="" class="layout-row">
        <div>
          <md-icon aria-hidden="true" class="venueDetailsComponent__icon material-icons" role="img">place</md-icon>
        </div>
        <div class="location-text">SVL CRSM1265, Floor 1
1265 Crossman Avenue, Sunnyvale, CA 94089, US</div>
      </div>
    </a><!---->
    <div class="schedule layout-row" layout="">
      <div>
        <md-icon aria-hidden="true" class="venueDetailsComponent__icon material-icons" role="img">schedule</md-icon>
      </div>
      <div>
        <div class="schedule-date">today, May 5</div>
        <!---->
        <div layout="column" class="layout-column">
          <!----><div ng-repeat="period in ctrl.dailySchedule">
            <div ng-class="{'green': ctrl.isServing(period) && !period.isClosedForSpecialHours}">
              <span>Lunch:</span>
              <!---->
              <!----><span ng-if="!period.isClosedForSpecialHours">11:30 am - 2:30 pm</span><!---->
            </div>
          </div><!---->
        </div>
      </div>
    </div>
    <div class="schedule-button">
      <button class="md-primary md-button md-ink-ripple" type="button" ng-transclude="" ng-click="ctrl.openWeeklySchedule($event)">
        View full schedule
      </button>
    </div>
  </div>

</div>
</food-venue-card-details>
          </div>
          <div layout="column" layout-align="start end" class="status-and-more layout-align-start-end layout-column">
            <!----><food-status class="food-status layout-align-end-end layout-row" layout="" layout-align="end end" status="ctrl.venues.getStatus(venueId)" ng-if="ctrl.venues.getStatus(venueId)"><div class="foodStatusComponent">
  <!----><div class="foodStatusComponent__container layout-row status-red" layout="row" ng-class="ctrl.getCssClassForStatus()" ng-if="ctrl.status">
    <div class="foodStatusComponent__container--content layout-row" layout="">
      <!----><span ng-repeat="statusPart in ctrl.status.statusTextParts" ng-class="{'bold': $index == ctrl.status.highlightAtIndex}">
        Opens&nbsp;
      </span><!----><span ng-repeat="statusPart in ctrl.status.statusTextParts" ng-class="{'bold': $index == ctrl.status.highlightAtIndex}" class="bold">
        Tue&nbsp;
      </span><!---->
    </div>
  </div><!---->
</div>
</food-status><!---->
            <button class="print-hidden details-toggle md-button md-ink-ripple" type="button" ng-transclude="" aria-describedby="venue-name-4391164" aria-expanded="false" role="button" ng-click="expanded = !expanded; ctrl.toggleVenueDetails(expanded)">
              More info
              <md-icon aria-hidden="true" class="md-icon material-icons" role="img">expand_more
              </md-icon>
            </button>
          </div>
        </div>
        <!---->
        <md-divider></md-divider>
        <!---->
        <!---->
        <!----><food-venue-card-menu class="dishMenuComponent layout-row flex" flex="" layout="" ng-if="ctrl.venues.getMenu(venueId)" menu="ctrl.venues.getMenu(venueId)" venue-id="venueId" ><div class="dishMenuComponent">
  <div class="venue-station dishMenuComponent__container layout-column flex" flex="" layout="column">
    <!---->
    <!----><div class="venue-station-item" ng-repeat="station in ctrl.menu.stations" >
      <h4 aria-description="The Studio is expanded" aria-expanded="true" aria-label="The Studio" class="station-header layout-align-start-center layout-row" layout="" layout-align="start center" ng-click="ctrl.toggleCollapsedStation($event, station.stationId)" role="heading" tabindex="0">
        <span flex="" class="flex">The Studio</span>
        <!---->
        <md-icon aria-hidden="true" class="print-hidden material-icons" role="img">
          expand_less
        </md-icon>
      </h4>
      <!----><!----><div ng-if="!ctrl.isStationCollapsed(station.stationId)" ng-repeat="dish in station.dishList.dishes">
        <food-dish-item dish="dish" venue-id="ctrl.venueId"><div class="dishItemComponent">
  <div aria-expanded="true" class="dish dishItemComponent__container expandable" ng-class="(ctrl.containsAvoidance() ? 'avoidance' : '')" role="presentation">
    <div class="venue-dish-header layout-row" layout="" ng-click="ctrl.toggleDish($event)" role="button" tabindex="0">
      <div class="venue-dish-title layout-row flex" flex="" layout="">
      <!---->
        <h5 aria-label="Punj Ratani Dal" class="dish-header-button flex" dir="ltr" flex="">
          Punj Ratani Dal
        </h5>
      </div>
      <!----><div class="diet-tags" ng-if="ctrl.showDiet()">
        <h6 class="food-invisible-text">Diet</h6>
        <!----><div class="diet-item Vegetarian layout-align-end-center layout-row" layout="" layout-align="end center" ng-repeat="tag in ctrl.dish.dietTags">
          Vegetarian
        </div><!---->
      </div><!---->
      <md-icon aria-hidden="true" class="dish-expand-button-icon material-icons" role="img">
        expand_less
      </md-icon>
    </div>
    <!----><div class="dish-details" ng-if="ctrl.isDishExpanded">
      <!----><div class="highlighting-reason" ng-if="ctrl.shouldShowRationales()">
        <div class="allergy-container">
        <!---->
          <!---->
          <!---->
        </div>
      </div><!---->
      <!----><div class="allergens selectable layout-align-start-start layout-row" layout="" layout-align="start start" ng-if="ctrl.shouldShowAllergens()">
        <div>
          <h6 class="dishItemComponent__allergens-title">Contains:&nbsp;</h6>
          Milk
        </div>
      </div><!---->
      <div class="ingredients selectable layout-row" dir="ltr" layout="">
        <div class="ingredients-expand-button">
          <h6 class="dishItemComponent__ingredients-title">Ingredients:&nbsp;</h6>
        </div>
        <span class="ingredients-list" ng-class="{'separated': ctrl.dataStore.hasUserDeskCountryMigrated}">
          Black Urad Dal, Yellow Moong Dal, Heavy Cream, Unsalted Butter, Kosher Salt
        </span>
      </div>
      <!----><div class="feedback-actions layout-row" layout="" ng-if="ctrl.shouldShowFeedbackActions()">
        <div class="feedback-action feedback-action-like layout-align-center-center layout-row" layout="" layout-align="center center">
          <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="0 likes" aria-pressed="false" ng-click="$event.stopPropagation(); ctrl.toggleLike($event)" tabindex="0">
            <md-icon aria-hidden="true" class="feedback-action__icon material-icons" ng-class="{active: ctrl.dish.isLiked}" role="img">
              thumb_up_off_alt
            </md-icon>
            
          </button>
          <!----><span aria-hidden="true" class="feedback-count" ng-if="!ctrl.isLoading">
          
        </span><!---->
          <!---->
        </div>
        <div class="feedback-action feedback-action-comment layout-align-center-center layout-row" layout="" layout-align="center center">
          <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="0 comments" ng-click="$event.stopPropagation(); ctrl.openDishCommentsDialog($event)" tabindex="0">
            <md-icon aria-hidden="true" class="feedback-action__icon material-icons" role="img">chat_bubble_outline</md-icon>
            
          </button>
          <span aria-hidden="true" class="feedback-count">
          
        </span>
        </div>
      </div><!---->
    </div><!---->
  </div>
  <md-divider class="venue-dish-divider"></md-divider>
</div>
</food-dish-item>
      </div><!----><!----><!----><div ng-if="!ctrl.isStationCollapsed(station.stationId)" ng-repeat="dish in station.dishList.dishes">
        <food-dish-item dish="dish" venue-id="ctrl.venueId"><div class="dishItemComponent">
  <div aria-expanded="true" class="dish dishItemComponent__container expandable" ng-class="(ctrl.containsAvoidance() ? 'avoidance' : '')" role="presentation">
    <div class="venue-dish-header layout-row" layout="" ng-click="ctrl.toggleDish($event)" role="button" tabindex="0">
      <div class="venue-dish-title layout-row flex" flex="" layout="">
      <!---->
        <h5 aria-label="Mushroom & Squash Biryani" class="dish-header-button flex" dir="ltr" flex="">
          Mushroom & Squash Biryani
        </h5>
      </div>
      <!----><div class="diet-tags" ng-if="ctrl.showDiet()">
        <h6 class="food-invisible-text">Diet</h6>
        <!----><div class="diet-item Vegetarian layout-align-end-center layout-row" layout="" layout-align="end center" ng-repeat="tag in ctrl.dish.dietTags">
          Vegetarian
        </div><!---->
      </div><!---->
      <md-icon aria-hidden="true" class="dish-expand-button-icon material-icons" role="img">
        expand_less
      </md-icon>
    </div>
    <!----><div class="dish-details" ng-if="ctrl.isDishExpanded">
      <!----><div class="highlighting-reason" ng-if="ctrl.shouldShowRationales()">
        <div class="allergy-container">
        <!---->
          <!---->
          <!---->
        </div>
      </div><!---->
      <!----><div class="allergens selectable layout-align-start-start layout-row" layout="" layout-align="start start" ng-if="ctrl.shouldShowAllergens()">
        <div>
          <h6 class="dishItemComponent__allergens-title">Contains:&nbsp;</h6>
          Milk
        </div>
      </div><!---->
      <div class="ingredients selectable layout-row" dir="ltr" layout="">
        <div class="ingredients-expand-button">
          <h6 class="dishItemComponent__ingredients-title">Ingredients:&nbsp;</h6>
        </div>
        <span class="ingredients-list" ng-class="{'separated': ctrl.dataStore.hasUserDeskCountryMigrated}">
          Roasted Squash (Butternut Squash, Canola Oil, Kosher Salt), Sauteed Mushroom (Trumpet Royale Mushroom, Button Mushroom, Canola Oil), Biryani Sauce (Whole Milk Yogurt, Garam Masala, Cinnamon Stick, Garlic, Ginger, Green Cardamom, Cumin Powder, Serrano Chile, Star Anise, Ground Clove, Bay Leaf), Onion Tomato Masala (Diced Tomato, Yellow Onion, Garlic, Ginger, Garam Masala, Turmeric Powder, Cumin Powder, Coriander Powder, Rice Bran Oil, Kasoori Methi), Steamed Basmati Rice (Basmati Rice, Kosher Salt), Fried Pure Onion, Mint
        </span>
      </div>
      <!----><div class="feedback-actions layout-row" layout="" ng-if="ctrl.shouldShowFeedbackActions()">
        <div class="feedback-action feedback-action-like layout-align-center-center layout-row" layout="" layout-align="center center">
          <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="0 likes" aria-pressed="false" ng-click="$event.stopPropagation(); ctrl.toggleLike($event)" tabindex="0">
            <md-icon aria-hidden="true" class="feedback-action__icon material-icons" ng-class="{active: ctrl.dish.isLiked}" role="img">
              thumb_up_off_alt
            </md-icon>
            
          </button>
          <!----><span aria-hidden="true" class="feedback-count" ng-if="!ctrl.isLoading">
          
        </span><!---->
          <!---->
        </div>
        <div class="feedback-action feedback-action-comment layout-align-center-center layout-row" layout="" layout-align="center center">
          <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="0 comments" ng-click="$event.stopPropagation(); ctrl.openDishCommentsDialog($event)" tabindex="0">
            <md-icon aria-hidden="true" class="feedback-action__icon material-icons" role="img">chat_bubble_outline</md-icon>
            
          </button>
          <span aria-hidden="true" class="feedback-count">
          
        </span>
        </div>
      </div><!---->
    </div><!---->
  </div>
  <md-divider class="venue-dish-divider"></md-divider>
</div>
</food-dish-item>
      </div><!----><!---->
      <!---->
    </div><!---->
    <!----><div class="discouraged-dishes-header print-hidden layout-align-start-center layout-row" layout="" layout-align="none center" ng-if="ctrl.menu.hasHiddenDishes()" >
      <h4 ng-class="{'hide-show-all-button': ctrl.showDiscouragedDishes || !ctrl.showDiscouragedDishes}" class="hide-show-all-button">
        1 dishes filtered out (from Eater Profile)
      </h4>
      <span flex="" class="flex"></span>
      <button class="md-button md-ink-ripple" type="button" ng-transclude="" aria-expanded="false" ng-click="ctrl.showDiscouragedDishes = !ctrl.showDiscouragedDishes">
        Show all
      </button>
    </div><!---->
    <!----><!----><!---->
  </div>
</div>
</food-venue-card-menu><!---->
      </div>
      <!---->
    </md-card><!---->
  </div><!---->

  
  <!---->
</div>
</food-venue-cards-list>
          </div><!---->
        </div>
      </md-content>
      <button class="md-raised mainEatComponent__back-to-top md-button md-ink-ripple" type="button" ng-transclude="" id="back-to-top" ng-click="ctrl.scrollTo('cards-inner-container')">
        Back to top
      </button>
    </div>
  </div>
</div>
</ng-view>



<script>
  // Wait for the DOM to be fully loaded before generating images
  document.addEventListener('DOMContentLoaded', async function() {
    const API_KEY = 'AIzaSyAbLkAK41p8jeOPN7uCm4I_rXDJuw97Z7o'; // Hardcoded API Key
    const BASE_PROMPT = "professional photo of {dish_name} made with {ingredients} on a table in a restaurant. Natural lighting, close-up shot from a slightly high angle. shallow dof. no distractions";

    const dishElements = document.querySelectorAll('.dishItemComponent');

    for (const dishItemComponent of dishElements) {
      const dishNameElement = dishItemComponent.querySelector('.dish-header-button');
      const ingredientsElement = dishItemComponent.querySelector('.ingredients-list');
      const generateButton = dishItemComponent.querySelector('.generate-dish-image-btn');


      const dishName = dishNameElement ? dishNameElement.textContent.trim() : '';
      const ingredients = ingredientsElement ? ingredientsElement.textContent.trim() : '';

      if (!dishName) {
        console.error('Dish name not found for a dish item.');
        continue; // Skip to the next dish if name is missing
      }

      const prompt = BASE_PROMPT.replace('{dish_name}', dishName).replace('{ingredients}', ingredients ? ` with ingredients ${ingredients}` : '');

      if (generateButton) {
        generateButton.disabled = true;
        generateButton.textContent = 'Generating...';
      }


      // Remove any previously generated image for this dish
      const existingImage = dishItemComponent.querySelector('.generated-dish-image');
      if (existingImage) {
        existingImage.remove();
      }

      try {
        // Assuming 'imagen-3.0-generate-002:predict' is the desired model based on script.js example
        // const selectedModel = 'imagen-3.0-generate-002:predict';
        // const selectedModel = 'gemini-2.0-flash-preview-image-generation';
        const selectedModel = 'imagen-3.0-fast-generate-001';
        const fetchUrl = `https://generativelanguage.googleapis.com/v1beta/models/${selectedModel}?key=${API_KEY}`;

        const requestBody = {
          instances: [{
            prompt: prompt,
            negative_prompt:"silverware or other dishware"
          }],
          parameters: {
            sampleCount: 1
          }
        };

        const response = await fetch(fetchUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error.message || `HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        let base64ImageData = null;

        if (data.predictions && data.predictions.length > 0 && data.predictions[0].bytesBase64Encoded) {
          base64ImageData = data.predictions[0].bytesBase64Encoded;
        }

        if (base64ImageData) {
          const img = document.createElement('img');
          img.src = `data:image/png;base64,${base64ImageData}`;
          img.classList.add('generated-dish-image'); // Add a class for identification
          img.style.maxWidth = '100%'; // Basic styling
          img.style.height = 'auto';
          img.style.marginTop = '10px';
          const dishHeader = dishItemComponent.querySelector('.venue-dish-header');
          if (dishHeader) {
            // Insert the image immediately after the .venue-dish-header element
            dishHeader.after(img);
          } else {
            // Fallback if .venue-dish-header is not found (should be rare)
            console.warn(`Could not find .venue-dish-header for dish: ${dishName}. Appending to dishItemComponent as a fallback.`);
            dishItemComponent.appendChild(img);
          }
        } else {
          console.error(`No image data received for dish: ${dishName}`);
          // Optionally display an error message on the page for this specific dish
        }

      } catch (error) {
        console.error(`Error generating image for dish: ${dishName}`, error);
        // Optionally display an error message on the page for this specific dish
      } finally {
        if (generateButton) {
          generateButton.disabled = false;
          generateButton.textContent = 'Generate Dish Image';
        }
      }
    }
  });
</script>
</body></html>
