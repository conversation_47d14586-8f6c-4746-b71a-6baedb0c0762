document.addEventListener('DOMContentLoaded', function() {
  const processPageBtn = document.getElementById('processPageBtn');
  const autoLoadCheckbox = document.getElementById('autoLoadCheckbox');

  let isProcessing = false;
  let currentTabId = null;

  // Auto-load checkbox functionality
  autoLoadCheckbox.addEventListener('change', function() {
    const isEnabled = autoLoadCheckbox.checked;

    // Save the setting
    chrome.storage.sync.set({
      'autoLoadEnabled': isEnabled
    });

    // Send message to all eat.googleplex.com tabs to update their auto-load setting
    chrome.tabs.query({url: "https://eat.googleplex.com/*"}, function(tabs) {
      tabs.forEach(tab => {
        chrome.tabs.sendMessage(tab.id, {
          action: 'updateAutoLoadSetting',
          enabled: isEnabled
        }, function(response) {
          if (chrome.runtime.lastError) {
            // Tab might not have content script loaded, ignore error
          }
        });
      });
    });
  });

  function loadAutoLoadSetting() {
    chrome.storage.sync.get(['autoLoadEnabled'], function(result) {
      // Default to true (enabled) if not set
      const isEnabled = result.autoLoadEnabled !== undefined ? result.autoLoadEnabled : true;
      autoLoadCheckbox.checked = isEnabled;
    });
  }

  // Process Current Page functionality (now "Load Images")
  processPageBtn.addEventListener('click', function() {
    if (isProcessing) {
      // Stop processing
      stopProcessing();
      return;
    }

    // Start processing
    startProcessing();
  });

  function startProcessing() {
    // Update state
    isProcessing = true;
    processPageBtn.textContent = 'Stop Loading';
    processPageBtn.style.backgroundColor = '#d32f2f';
    processPageBtn.disabled = true; // Disable button while processing

    // Get the active tab and send message to content script
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      if (tabs.length === 0) {
        console.error('No active tab found');
        stopProcessing();
        return;
      }

      currentTabId = tabs[0].id;
      const tabUrl = tabs[0].url;

      // Check if we're on the correct domain (allow local files for testing)
      if (!tabUrl.includes('eat.googleplex.com') && !tabUrl.startsWith('file://')) {
        console.error('This extension only works on eat.googleplex.com. Please navigate to the Google cafeteria website.');
        stopProcessing();
        return;
      }

      // Send message to content script to process the page
      chrome.tabs.sendMessage(currentTabId, {action: 'processPage'}, function(response) {
        if (chrome.runtime.lastError) {
          console.error(`Chrome runtime error: ${chrome.runtime.lastError.message}`);
          stopProcessing();
          return;
        }

        if (response && response.success) {
          console.log(`Processing completed successfully: ${response.message}`);
        } else {
          const errorMsg = response ? response.message : 'Failed to process page';
          console.error(`Processing failed: ${errorMsg}`);
        }

        stopProcessing();
      });
    });
  }

  function stopProcessing() {
    if (currentTabId && isProcessing) {
      // Send stop message to content script
      chrome.tabs.sendMessage(currentTabId, {action: 'stopProcessing'}, function(response) {
        if (chrome.runtime.lastError) {
          console.warn(`Error sending stop message: ${chrome.runtime.lastError.message}`);
        }
      });
    }

    // Reset state
    isProcessing = false;
    currentTabId = null;
    processPageBtn.disabled = false;
    processPageBtn.textContent = 'Load Images';
    processPageBtn.style.backgroundColor = '#34a853';
  }

  // Initialize - load existing state from storage
  loadAutoLoadSetting();
});
