// Firebase-Only Content Script (No Local Cache)
// This script handles DOM manipulation and communicates with background script for Firebase Storage operations

console.log('🔥 Firebase-only content script loaded');

// Configuration for parallel processing
const PARALLEL_CONFIG = {
  maxConcurrentRequests: 3,
  batchSize: 5,
  delayBetweenBatches: 1000
};

// Global state
let isProcessingStopped = false;
let pendingRequests = new Map(); // For deduplication

// Main function to process all dishes on the page
async function processAllDishes() {
  console.log('🚀 Starting Firebase-only dish processing...');

  const dishElements = await getDishElements();
  if (dishElements.length === 0) {
    console.log('No dish elements found on page');
    return { success: false, message: 'No dishes found on page' };
  }

  console.log(`🔥 Processing ${dishElements.length} dishes using Firebase Storage only`);

  // Analyze dishes for processing
  const dishAnalysis = await analyzeDishesForFirebaseProcessing(dishElements);
  console.log(`📊 Analysis: ${dishAnalysis.valid.length} valid, ${dishAnalysis.skipped.length} skipped`);

  // Show loading indicators
  showLoadingIndicators(dishAnalysis.valid);

  // Process all dishes in parallel batches
  const results = await processFirebaseBatches(dishAnalysis.valid);

  // Compile results
  const totalProcessed = results.firebase + results.generated;
  const totalSkipped = dishAnalysis.skipped.length;

  let message = `Processed ${totalProcessed} dishes`;
  if (totalSkipped > 0) {
    message += `, skipped ${totalSkipped}`;
  }

  const sourceBreakdown = [];
  if (results.firebase > 0) sourceBreakdown.push(`${results.firebase} from Firebase`);
  if (results.generated > 0) sourceBreakdown.push(`${results.generated} generated`);
  
  if (sourceBreakdown.length > 0) {
    message += ` (${sourceBreakdown.join(', ')})`;
  }

  console.log(`✅ Firebase-only processing complete: ${message}`);
  return { success: true, message, metrics: results };
}

// Wait for page to be fully loaded and elements to be available
async function waitForPageLoad() {
  console.log('⏳ Waiting for page to fully load...');

  // Wait for DOM to be ready
  if (document.readyState !== 'complete') {
    await new Promise(resolve => {
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', resolve);
      } else {
        window.addEventListener('load', resolve);
      }
    });
  }

  // Additional wait for dynamic content (Angular/React apps)
  let attempts = 0;
  const maxAttempts = 10;

  while (attempts < maxAttempts) {
    const dishElements = document.querySelectorAll('.dish, .venue-dish-header, h5.dish-header-button');

    if (dishElements.length > 0) {
      console.log(`✅ Page loaded! Found ${dishElements.length} potential dish elements after ${attempts} attempts`);
      return;
    }

    console.log(`⏳ Attempt ${attempts + 1}/${maxAttempts}: No dish elements found, waiting...`);
    await new Promise(resolve => setTimeout(resolve, 500));
    attempts++;
  }

  console.log('⚠️ Page load timeout - proceeding anyway');
}

// Get all dish elements from the page
async function getDishElements() {
  // Wait for page to be fully loaded first
  await waitForPageLoad();

  // Debug: Check what elements are actually on the page
  console.log('🔍 Debugging page elements...');
  console.log('📄 Page URL:', window.location.href);
  console.log('📄 Page title:', document.title);
  console.log('📄 Ready state:', document.readyState);

  // Try different selectors to see what's available
  const debugSelectors = [
    '.dish',
    '.dishItemComponent__container',
    '.venue-dish-header',
    '.dish.dishItemComponent__container',
    'h5.dish-header-button',
    '[class*="dish"]',
    '[class*="venue"]'
  ];

  debugSelectors.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    console.log(`🔍 Selector "${selector}": ${elements.length} elements`);
    if (elements.length > 0 && elements.length <= 3) {
      console.log(`📝 Sample elements for "${selector}":`, Array.from(elements).slice(0, 2));
    }
  });

  // Look for dish containers that contain the venue-dish-header
  const allDishElements = document.querySelectorAll('.dish.dishItemComponent__container, .venue-dish-header');
  console.log(`🔍 Found ${allDishElements.length} dish elements on page`);

  const validDishElements = Array.from(allDishElements).filter(element => {
    const dishName = extractDishName(element);
    const isValid = dishName && dishName.trim().length > 0;
    if (!isValid) {
      console.log('⚠️ Skipping dish element with no name:', element);
    }
    return isValid;
  });

  console.log(`✅ Found ${validDishElements.length} valid dish elements with names`);
  return validDishElements;
}

// Extract dish name from element
function extractDishName(element) {
  // Try multiple possible selectors for dish names
  const possibleSelectors = [
    'h5.dish-header-button',  // New format: <h5 class="dish-header-button">
    '.venue-dish-name',       // Old format (fallback)
    'h5',                     // Generic h5 fallback
    '.dish-name'              // Another possible format
  ];

  let nameElement = null;
  let dishName = null;

  for (const selector of possibleSelectors) {
    nameElement = element.querySelector(selector);
    if (nameElement) {
      dishName = nameElement.textContent.trim();
      if (dishName) {
        console.log(`📝 Extracted dish name: "${dishName}" (using selector: ${selector})`);
        return dishName;
      }
    }
  }

  console.log('⚠️ No dish name found in element:', element);
  console.log('⚠️ Tried selectors:', possibleSelectors);
  console.log('⚠️ Element HTML:', element.outerHTML.substring(0, 200) + '...');

  return null;
}

// Extract ingredients from element
function extractIngredients(element) {
  // Try multiple possible selectors for dish descriptions/ingredients
  const possibleSelectors = [
    '.venue-dish-description',  // Old format
    '.dish-description',        // Possible new format
    '.description',             // Generic description
    'p',                        // Generic paragraph
    '.ingredients'              // Direct ingredients
  ];

  for (const selector of possibleSelectors) {
    const ingredientsElement = element.querySelector(selector);
    if (ingredientsElement) {
      const description = ingredientsElement.textContent.trim();
      if (description) {
        const ingredients = description.split(/[,;]/).map(ingredient => ingredient.trim()).filter(ingredient => ingredient.length > 0);
        console.log(`📝 Extracted ${ingredients.length} ingredients using selector: ${selector}`);
        return ingredients;
      }
    }
  }

  console.log('⚠️ No ingredients found for element');
  return [];
}

// Analyze dishes for Firebase processing (no cache checking)
async function analyzeDishesForFirebaseProcessing(dishElements) {
  const analysis = {
    valid: [],
    skipped: []
  };

  for (const element of dishElements) {
    const dishName = extractDishName(element);
    const ingredients = extractIngredients(element);

    if (!dishName || dishName.trim().length === 0) {
      analysis.skipped.push({ element, reason: 'No dish name' });
      continue;
    }

    analysis.valid.push({
      element,
      dishName: dishName.trim(),
      ingredients
    });
  }

  return analysis;
}

// Show loading indicators for all dishes
function showLoadingIndicators(validDishes) {
  validDishes.forEach(dishInfo => {
    showLoadingIndicator(dishInfo.element, dishInfo.dishName);
  });
}

// Show loading indicator for a specific dish
function showLoadingIndicator(dishElement, dishName) {
  // Remove any existing loading indicator
  removeLoadingIndicator(dishElement);

  const loader = document.createElement('div');
  loader.className = 'dish-image-loading';
  loader.setAttribute('data-dish-name', dishName);

  loader.style.cssText = `
    padding: 10px;
    margin: 5px 0;
    background-color: #e3f2fd;
    border: 1px solid #4285f4;
    border-radius: 4px;
    color: #1565c0;
    font-size: 12px;
    text-align: center;
    animation: pulse 1.5s ease-in-out infinite alternate;
  `;

  loader.textContent = '🔥 Loading from Firebase...';

  // Add CSS animation
  if (!document.getElementById('loading-animation-style')) {
    const style = document.createElement('style');
    style.id = 'loading-animation-style';
    style.textContent = `
      @keyframes pulse {
        from { opacity: 0.6; }
        to { opacity: 1; }
      }
    `;
    document.head.appendChild(style);
  }

  dishElement.appendChild(loader);
}

// Remove loading indicator
function removeLoadingIndicator(dishElement) {
  const loader = dishElement.querySelector('.dish-image-loading');
  if (loader) {
    loader.remove();
  }
}

// Process dishes in Firebase-only batches
async function processFirebaseBatches(validDishes) {
  const results = {
    firebase: 0,
    generated: 0,
    errors: 0
  };

  // Split into batches
  const batches = [];
  for (let i = 0; i < validDishes.length; i += PARALLEL_CONFIG.batchSize) {
    batches.push(validDishes.slice(i, i + PARALLEL_CONFIG.batchSize));
  }

  console.log(`📊 Processing ${validDishes.length} dishes in ${batches.length} batches`);

  // Process each batch
  for (let i = 0; i < batches.length; i++) {
    if (isProcessingStopped) break;

    const batch = batches[i];
    console.log(`🔄 Processing batch ${i + 1}/${batches.length} (${batch.length} dishes)`);

    // Process batch in parallel
    const batchPromises = batch.map(dishInfo => processSingleDish(dishInfo));
    const batchResults = await Promise.all(batchPromises);

    // Update results
    batchResults.forEach(result => {
      if (result && result.success) {
        if (result.source === 'firebase') {
          results.firebase++;
        } else if (result.source === 'generated') {
          results.generated++;
        }
      } else {
        results.errors++;
      }
    });

    // Delay between batches
    if (i < batches.length - 1) {
      await new Promise(resolve => setTimeout(resolve, PARALLEL_CONFIG.delayBetweenBatches));
    }
  }

  return results;
}

// Process a single dish
async function processSingleDish(dishInfo) {
  try {
    console.log(`🔥 Processing: ${dishInfo.dishName}`);

    // Send request to background script for Firebase processing
    const response = await sendBackgroundRequest(dishInfo.dishName, dishInfo.ingredients);

    if (response && response.success) {
      // Remove loading indicator
      removeLoadingIndicator(dishInfo.element);

      // Insert image
      insertImageIntoDish(dishInfo.element, response.imageData, response.mimeType);

      const sourceDescription = getSourceDescription(response.source);
      console.log(`✅ ${dishInfo.dishName}: ${sourceDescription}`);

      return { success: true, source: response.source };
    } else {
      console.error(`❌ Failed to process ${dishInfo.dishName}:`, response?.error || 'Unknown error');
      removeLoadingIndicator(dishInfo.element);
      return { success: false, error: response?.error };
    }
  } catch (error) {
    console.error(`❌ Error processing ${dishInfo.dishName}:`, error);
    removeLoadingIndicator(dishInfo.element);
    return { success: false, error: error.message };
  }
}

// Send request to background script with deduplication
async function sendBackgroundRequest(dishName, ingredients) {
  const requestKey = dishName;

  // Check if request is already pending
  if (pendingRequests.has(requestKey)) {
    console.log(`⏳ Request for "${dishName}" already pending, waiting...`);
    return await pendingRequests.get(requestKey);
  }

  // Create new request
  const requestPromise = new Promise((resolve) => {
    chrome.runtime.sendMessage({
      action: 'getOrGenerateDishImage',
      dishName: dishName,
      ingredients: ingredients,
      model: 'imagen-3.0-generate-002'
    }, (response) => {
      resolve(response);
    });
  });

  // Store pending request
  pendingRequests.set(requestKey, requestPromise);

  try {
    const result = await requestPromise;
    return result;
  } finally {
    // Clean up pending request
    pendingRequests.delete(requestKey);
  }
}

// Insert image into dish element
function insertImageIntoDish(dishElement, imageData, mimeType) {
  // Remove any existing image
  const existingImage = dishElement.querySelector('.generated-dish-image');
  if (existingImage) {
    existingImage.remove();
  }

  // Create new image element
  const img = document.createElement('img');
  img.className = 'generated-dish-image';
  img.src = `data:${mimeType};base64,${imageData}`;
  img.style.cssText = `
    max-width: 200px;
    max-height: 150px;
    border-radius: 8px;
    margin: 10px 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    display: block;
    width: 100%;
    clear: both;
    flex: none;
  `;

  // Find the correct insertion point: after the dish header, on its own row
  const dishHeader = dishElement.querySelector('.venue-dish-header');
  const dishDetails = dishElement.querySelector('.dish-details');

  if (dishHeader && dishDetails) {
    // Insert between dish header and dish details (outside both containers)
    dishHeader.parentNode.insertBefore(img, dishDetails);
    console.log(`📍 Inserted image between header and details (on own row) for: ${extractDishName(dishElement)}`);
  } else if (dishHeader) {
    // Fallback: insert after dish header using parentNode
    dishHeader.parentNode.insertBefore(img, dishHeader.nextSibling);
    console.log(`📍 Inserted image after dish header for: ${extractDishName(dishElement)}`);
  } else {
    // Last resort: append to dish element
    dishElement.appendChild(img);
    console.log(`📍 Appended image to dish element for: ${extractDishName(dishElement)}`);
  }
}

// Get source description
function getSourceDescription(source) {
  switch (source) {
    case 'firebase':
      return '🔥 Retrieved from Firebase Storage';
    case 'generated':
      return '🎨 Generated new image using AI';
    default:
      return `❓ Unknown source: ${source}`;
  }
}

// Process individual dish (for manual processing)
async function processIndividualDish(dishElement) {
  const dishName = extractDishName(dishElement);
  const ingredients = extractIngredients(dishElement);

  if (!dishName) {
    console.log('No dish name found for individual processing');
    return;
  }

  const dishInfo = { element: dishElement, dishName, ingredients };
  showLoadingIndicator(dishElement, dishName);
  await processSingleDish(dishInfo);
}

// Listen for messages from popup or background script
chrome.runtime.onMessage.addListener(async (request, sender, sendResponse) => {
  if (request.action === 'processPage') {
    console.log('🔥 Received request to process page (Firebase-only mode)');
    
    try {
      const result = await processAllDishes();
      sendResponse(result);
    } catch (error) {
      console.error('Error processing page:', error);
      sendResponse({ success: false, error: error.message });
    }
  }
  
  return true; // Keep message channel open for async response
});

// Auto-load functionality - check if auto-load is enabled
async function checkAutoLoad() {
  try {
    const result = await new Promise((resolve) => {
      chrome.storage.local.get(['autoLoadEnabled'], (result) => {
        resolve(result);
      });
    });

    const autoLoadEnabled = result.autoLoadEnabled !== false; // Default to true

    if (autoLoadEnabled) {
      console.log('🚀 Auto-load enabled, processing page automatically...');
      // Small delay to ensure page is fully loaded
      setTimeout(async () => {
        try {
          await processAllDishes();
        } catch (error) {
          console.error('Error in auto-load:', error);
        }
      }, 1000);
    } else {
      console.log('⏸️ Auto-load disabled');
    }
  } catch (error) {
    console.error('Error checking auto-load setting:', error);
  }
}

// Initialize auto-load check when page loads
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', checkAutoLoad);
} else {
  // Page already loaded
  checkAutoLoad();
}

console.log('🔥 Firebase-only content script ready');
