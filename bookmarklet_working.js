javascript:(function(){
  const API_KEY='AIzaSyAbLkAK41p8jeOPN7uCm4I_rXDJuw97Z7o';
  const BASE_PROMPT="professional photo of {dish_name} made with {ingredients} on a table in a restaurant. Natural lighting, close-up shot from a slightly high angle. shallow dof. no distractions";

  async function generateImageForDish(dishItemComponent){
    const dishNameElement=dishItemComponent.querySelector('.dish-header-button');
    const ingredientsElement=dishItemComponent.querySelector('.ingredients-list');
    const dishName=dishNameElement?dishNameElement.textContent.trim():'';
    const ingredients=ingredientsElement?ingredientsElement.textContent.trim():'';

    if(!dishName){
      console.error('Dish name not found for a dish item.');
      return;
    }

    const prompt=BASE_PROMPT.replace('{dish_name}',dishName).replace('{ingredients}',ingredients?` with ingredients ${ingredients}`:'');
    const existingImage=dishItemComponent.querySelector('.generated-dish-image');
    if(existingImage){
      existingImage.remove();
    }

    try{
      const selectedModel='imagen-3.0-generate-002:predict';
      const fetchUrl=`https://generativelanguage.googleapis.com/v1beta/models/${selectedModel}?key=${API_KEY}`;
      const requestBody={
        instances:[{prompt:prompt}],
        parameters:{sampleCount:1}
      };

      const response=await fetch(fetchUrl,{
        method:'POST',
        headers:{'Content-Type':'application/json'},
        body:JSON.stringify(requestBody)
      });

      if(!response.ok){
        const errorData=await response.json();
        throw new Error(errorData.error.message||`HTTP error! status: ${response.status}`);
      }

      const data=await response.json();
      let base64ImageData=null;
      if(data.predictions&&data.predictions.length>0&&data.predictions[0].bytesBase64Encoded){
        base64ImageData=data.predictions[0].bytesBase64Encoded;
      }

      if(base64ImageData){
        const img=document.createElement('img');
        img.src=`data:image/png;base64,${base64ImageData}`;
        img.classList.add('generated-dish-image');
        img.style.maxWidth='100%';
        img.style.height='auto';
        img.style.marginTop='10px';

        const dishDetails=dishItemComponent.querySelector('.dish-details');
        const allergensElement=dishDetails?dishDetails.querySelector('.allergens'):null;
        if(dishDetails){
          if(allergensElement){
            dishDetails.insertBefore(img,allergensElement);
          }else{
            dishDetails.appendChild(img);
          }
        }else{
          console.error('Dish details element not found for image insertion.');
        }
      }else{
        console.error(`No image data received for dish: ${dishName}`);
      }
    }catch(error){
      console.error(`Error generating image for dish: ${dishName}`,error);
    }
  }

  const dishElements=document.querySelectorAll('.dishItemComponent');
  dishElements.forEach(generateImageForDish);
})();
