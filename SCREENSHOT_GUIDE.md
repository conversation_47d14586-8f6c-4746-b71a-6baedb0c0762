# Screenshot Creation Guide for Google Workspace Marketplace

## Requirements

### Technical Specifications
- **Minimum Size:** 1280 x 800 pixels
- **Maximum Size:** 2560 x 1600 pixels (recommended)
- **Format:** PNG or JPEG
- **Quantity:** 1-5 screenshots (at least 1 required)
- **Content:** Must show integration with Google services

### Content Requirements
- Show your extension working on eat.googleplex.com
- Demonstrate Google services integration (Drive, AI)
- Clear, professional appearance
- No personal information visible

## Recommended Screenshots

### Screenshot 1: Main Functionality (Required)
**Title:** "AI-Generated Dish Images on eat.googleplex.com"
**Content:**
- Browser showing eat.googleplex.com
- Dish listings with AI-generated images visible
- Extension icon visible in toolbar
- Clean, professional layout

**How to capture:**
1. Open eat.googleplex.com in Chrome
2. Ensure extension is working and images are showing
3. Use full browser window (1280x800 minimum)
4. Take screenshot showing multiple dishes with images

### Screenshot 2: Extension Popup (Recommended)
**Title:** "Extension Controls and Settings"
**Content:**
- Extension popup open showing controls
- Options like "Auto-generate images" checkbox
- Clear cache button
- Professional interface

**How to capture:**
1. Click extension icon to open popup
2. Ensure popup is fully visible
3. Capture browser with popup open
4. Show key features and controls

### Screenshot 3: Options Page (Recommended)
**Title:** "Extension Configuration Options"
**Content:**
- Extension options page open
- Settings and configuration options
- Debug information (if available)
- Professional layout

**How to capture:**
1. Right-click extension icon → Options
2. Show the options page
3. Capture full page with settings visible
4. Ensure all text is readable

### Screenshot 4: Google Drive Integration (Recommended)
**Title:** "Cached Images in Google Drive"
**Content:**
- Google Drive showing cached images folder
- Generated dish images stored in Drive
- Clear demonstration of Google integration

**How to capture:**
1. Open Google Drive
2. Navigate to cached images folder
3. Show generated images stored there
4. Demonstrate the Google services integration

### Screenshot 5: Image Generation Process (Optional)
**Title:** "AI Image Generation in Action"
**Content:**
- Loading states or generation process
- Before/after showing image appearing
- Demonstrates the AI functionality

## Screenshot Preparation Steps

### 1. Prepare Your Environment
```bash
# Ensure extension is installed and working
# Clear any personal data from browser
# Set browser to appropriate size (1280x800 minimum)
# Ensure good lighting for screen capture
```

### 2. Browser Setup
- Use Chrome in normal mode (not incognito)
- Clear any personal bookmarks/data from view
- Set zoom to 100%
- Close unnecessary tabs
- Ensure extension icon is visible

### 3. Content Preparation
- Have eat.googleplex.com loaded with dishes showing
- Ensure images are generated and visible
- Test extension functionality before capturing
- Prepare any demo scenarios

### 4. Capture Tools
**macOS:**
- Cmd+Shift+4 (select area)
- Cmd+Shift+3 (full screen)
- Use Preview or built-in tools

**Windows:**
- Snipping Tool
- Print Screen + Paint
- Windows+Shift+S (Windows 10+)

**Chrome Extensions:**
- Awesome Screenshot
- Lightshot
- Full Page Screen Capture

### 5. Post-Processing
- Ensure minimum 1280x800 resolution
- Crop to remove unnecessary elements
- Check that all text is readable
- Remove any personal information
- Save as PNG for best quality

## Screenshot Checklist

### Before Taking Screenshots
- [ ] Extension is installed and working
- [ ] Browser is set to appropriate size
- [ ] eat.googleplex.com is loaded and functional
- [ ] Images are generating properly
- [ ] No personal information visible
- [ ] Extension icon is visible in toolbar

### For Each Screenshot
- [ ] Minimum 1280x800 pixels
- [ ] Shows Google services integration
- [ ] Professional appearance
- [ ] Clear, readable text
- [ ] No personal data visible
- [ ] Demonstrates key functionality

### After Taking Screenshots
- [ ] Check file sizes and dimensions
- [ ] Verify image quality
- [ ] Ensure no personal information
- [ ] Test that images display properly
- [ ] Prepare descriptive captions

## File Naming Convention
```
screenshot-1-main-functionality.png
screenshot-2-extension-popup.png
screenshot-3-options-page.png
screenshot-4-drive-integration.png
screenshot-5-generation-process.png
```

## Upload Preparation

### File Organization
```
screenshots/
├── screenshot-1-main-functionality.png
├── screenshot-2-extension-popup.png
├── screenshot-3-options-page.png
├── screenshot-4-drive-integration.png
└── screenshot-5-generation-process.png
```

### Captions to Prepare
1. "AI-generated dish images enhance the eat.googleplex.com dining experience"
2. "Easy-to-use extension controls and settings"
3. "Comprehensive configuration options for customization"
4. "Seamless integration with Google Drive for image caching"
5. "Real-time AI image generation using Google's advanced technology"

## Quality Guidelines

### Do Include:
- Clear demonstration of functionality
- Google services integration
- Professional interface
- Key features and benefits
- Clean, uncluttered layout

### Don't Include:
- Personal information or data
- Error messages or broken states
- Cluttered or confusing interfaces
- Low-quality or blurry images
- Irrelevant browser elements

## Next Steps

1. **Take screenshots** following this guide
2. **Save in screenshots/ folder** with proper naming
3. **Review for quality** and compliance
4. **Upload to Marketplace SDK** in Store Listing section
5. **Add descriptive captions** for each screenshot

Need help with any specific screenshot or have questions about the requirements?
