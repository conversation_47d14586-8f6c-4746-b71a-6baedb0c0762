# Deployment Checklist

## ✅ Completed
- [x] Chrome extension code complete
- [x] All required icons created (16, 32, 48, 96, 128px)
- [x] Application banner created (220x140px)
- [x] Manifest.json properly configured
- [x] Google Cloud project exists (`eat-with-images`)
- [x] OAuth client configured

## 🔄 Immediate Next Steps (Do These First)

### 1. Create Screenshots (REQUIRED)
- [ ] Take screenshot of extension working on eat.googleplex.com
- [ ] Ensure screenshot is at least 1280 x 800 pixels
- [ ] Save as `screenshots/marketplace-screenshot-1.png`
- [ ] Optional: Create 2-5 total screenshots showing different features

### 2. Create Support Documentation (REQUIRED)
Create these three web pages:
- [ ] **Terms of Service** page
- [ ] **Privacy Policy** page
- [ ] **Support/Help** page

**Quick Option:** Use Google Sites or GitHub Pages to host these quickly.

### 3. Package Extension for Chrome Web Store
- [ ] Increment version number in manifest.json (e.g., 1.0 → 1.1)
- [ ] Remove key from manifest.json temporarily
- [ ] Create ZIP file: `zip -r eat_extension_deployment.zip . -x "*.git*" "*.DS_Store" "node_modules/*" "*.log" "images/*"`
- [ ] Restore key to manifest.json immediately after packaging
- [ ] Test ZIP file by loading as unpacked extension

## 📋 Deployment Steps (Do After Above)

### Phase 1: Chrome Web Store
- [ ] Go to [Chrome Web Store Developer Dashboard](https://chrome.google.com/webstore/devconsole/)
- [ ] Pay $5 developer fee (one-time)
- [ ] Upload extension ZIP
- [ ] Set to "Private" initially
- [ ] Note the Extension ID

### Phase 2: Google Cloud Console
- [ ] Enable Google Workspace Marketplace SDK
- [ ] Configure OAuth consent screen
- [ ] Add Extension ID to Marketplace SDK
- [ ] Configure app details

### Phase 3: Marketplace Store Listing
- [ ] Fill out app details
- [ ] Upload all icons and banner
- [ ] Upload screenshots
- [ ] Add support links
- [ ] Set distribution settings

### Phase 4: Testing
- [ ] Install extension from Chrome Web Store (private)
- [ ] Test all functionality
- [ ] Verify OAuth flows
- [ ] Test on eat.googleplex.com

### Phase 5: Submit for Review
- [ ] Final review of all materials
- [ ] Submit to Google Workspace Marketplace
- [ ] Monitor review status
- [ ] Address any feedback

### Phase 6: Go Live
- [ ] Receive approval
- [ ] Make Chrome Web Store listing public (optional)
- [ ] Monitor user feedback

## 🚨 Critical Requirements

1. **Screenshots must show Google integration** - This is key for approval
2. **Support documentation must be live** - Google will check these links
3. **Extension must work on eat.googleplex.com** - Test thoroughly
4. **OAuth consent screen must match app name** - Ensure consistency

## 📞 Need Help With?

Let me know if you need assistance with:
- Creating the required screenshots
- Setting up support documentation pages
- Packaging the extension
- Configuring Google Cloud settings
- Writing app descriptions
- Any other deployment steps

## ⏱️ Timeline Estimate

- **Preparation (Screenshots, docs):** 2-4 hours
- **Chrome Web Store upload:** 30 minutes
- **Marketplace configuration:** 1-2 hours
- **Google review process:** 1-2 weeks
- **Total time to live:** 2-3 weeks

## 🔗 Important Links

- [Chrome Web Store Developer Dashboard](https://chrome.google.com/webstore/devconsole/)
- [Google Cloud Console](https://console.cloud.google.com/)
- [Google Workspace Marketplace SDK](https://console.cloud.google.com/apis/api/appsmarket-component.googleapis.com/)
- [Marketplace Publishing Guide](https://developers.google.com/workspace/marketplace/how-to-publish)
