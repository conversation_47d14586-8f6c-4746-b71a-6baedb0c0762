# Chrome Extension Deployment Guide

## Overview
This guide provides step-by-step instructions for packaging the "Eat (with images)" Chrome extension for deployment to the Chrome Web Store. The process involves temporarily removing the development key, creating a deployment package, and restoring the key for continued development.

## Prerequisites
- Ensure all development and testing is complete
- Verify the extension works correctly in development mode
- Have terminal/command line access to the project directory

## Deployment Process

### Step 1: Pre-packaging Preparation

#### 1.1 Increment version number in manifest.json
**IMPORTANT:** Always increment the version number before packaging for deployment.

1. Open `manifest.json` in your editor
2. Locate line 4 which contains the version field:
   ```json
   "version": "1.0",
   ```
3. Increment the minor version number (e.g., "1.0" → "1.1", "1.1" → "1.2")
4. Save the file

#### 1.2 Remove the "key" field from manifest.json
**IMPORTANT:** The "key" field must be removed before packaging for Chrome Web Store submission.

1. Open `manifest.json` in your editor
2. Locate line 6 which contains the key field:
   ```json
   "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAv+i/FGLF/2fYNW4EVu/zAXws9tUzQfnmMJQmgRozC1uDuC8/h8Kku+/TiC01yVePody1i+KuyNIVb7Hh6+G6RDtDRdDzCDlk/Ebn7SpT+B0nmWz83tzIj652OH78B7Z40oaeFOyczYkIpiqLgqeGM/gSfx5hyhSeJU5zn56Eja53GFwV5MTE3VTt2VhOiSbVm7bpTg5CoNMhqqaWtB3dnZoUWGJQG5X/ttozCfVVeY9LPj+LsDgD4A3eLnpOUT0vHN2lFOCHrbxPLOR5lQRyKwtUxiAFglpTQwiPpWv6kJKrj2lfLyW2MYT6o1eCVTp0hKblEPEJU9wPYZ/hoQx7jwIDAQAB",
   ```
3. Delete the entire line including the comma
4. Save the file

#### 1.3 Save the key value for restoration
**CRITICAL:** Copy and save this exact key value for restoration after packaging:
```
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAv+i/FGLF/2fYNW4EVu/zAXws9tUzQfnmMJQmgRozC1uDuC8/h8Kku+/TiC01yVePody1i+KuyNIVb7Hh6+G6RDtDRdDzCDlk/Ebn7SpT+B0nmWz83tzIj652OH78B7Z40oaeFOyczYkIpiqLgqeGM/gSfx5hyhSeJU5zn56Eja53GFwV5MTE3VTt2VhOiSbVm7bpTg5CoNMhqqaWtB3dnZoUWGJQG5X/ttozCfVVeY9LPj+LsDgD4A3eLnpOUT0vHN2lFOCHrbxPLOR5lQRyKwtUxiAFglpTQwiPpWv6kJKrj2lfLyW2MYT6o1eCVTp0hKblEPEJU9wPYZ/hoQx7jwIDAQAB
```

### Step 2: Create Deployment Package

#### 2.1 Navigate to project directory
Open terminal and navigate to the extension's root directory:
```bash
cd /path/to/eat_extension
```

#### 2.2 Create the deployment zip file
Run the following command to create the deployment package:
```bash
zip -r eat_extension_deployment.zip . -x "*.git*" "*.DS_Store" "node_modules/*" "*.log" "images/*"
```

**Explanation of exclusions:**
- `*.git*` - Excludes Git repository files
- `*.DS_Store` - Excludes macOS system files
- `node_modules/*` - Excludes Node.js dependencies (if any)
- `*.log` - Excludes log files
- `images/*` - Excludes the images directory to reduce package size

#### 2.3 Verify package creation
Confirm the zip file was created successfully:
```bash
ls -la eat_extension_deployment.zip
```

### Step 3: Post-packaging Restoration

#### 3.1 Restore the "key" field to manifest.json
**IMMEDIATELY** after creating the zip file, restore the key to maintain development functionality:

1. Open `manifest.json` in your editor
2. Add the key field back as line 6, after the description line:
   ```json
   {
     "manifest_version": 3,
     "name": "Eat (with images)",
     "version": "1.1",
     "description": "Generate and cache images of dishes using Google's Generative AI",
     "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAv+i/FGLF/2fYNW4EVu/zAXws9tUzQfnmMJQmgRozC1uDuC8/h8Kku+/TiC01yVePody1i+KuyNIVb7Hh6+G6RDtDRdDzCDlk/Ebn7SpT+B0nmWz83tzIj652OH78B7Z40oaeFOyczYkIpiqLgqeGM/gSfx5hyhSeJU5zn56Eja53GFwV5MTE3VTt2VhOiSbVm7bpTg5CoNMhqqaWtB3dnZoUWGJQG5X/ttozCfVVeY9LPj+LsDgD4A3eLnpOUT0vHN2lFOCHrbxPLOR5lQRyKwtUxiAFglpTQwiPpWv6kJKrj2lfLyW2MYT6o1eCVTp0hKblEPEJU9wPYZ/hoQx7jwIDAQAB",
     "permissions": [
       ...
   ```
3. Save the file

### Step 4: Verification Steps

#### 4.1 Verify deployment package
1. Check that `eat_extension_deployment.zip` exists in the project directory
2. Verify the file size is reasonable (should be smaller without images directory)
3. Optionally, extract to a temporary location and verify contents

#### 4.2 Verify development environment restoration
1. Confirm `manifest.json` contains the "key" field on line 6
2. Test that the extension still loads properly in Chrome developer mode
3. Verify no functionality is broken in development

#### 4.3 Package contents verification
The deployment package should include:
- ✅ `manifest.json` (without key field)
- ✅ `popup.html` and related popup files
- ✅ `background.js`
- ✅ `content.js`
- ✅ `Code.js`
- ✅ `compression.js`
- ✅ `options.html` and related options files
- ✅ `icons/` directory with all icon files
- ❌ `images/` directory (excluded)
- ❌ `.git` files (excluded)
- ❌ Development files (excluded)

## Chrome Web Store Submission

After completing the packaging process:

1. Go to [Chrome Web Store Developer Dashboard](https://chrome.google.com/webstore/devconsole/)
2. Upload `eat_extension_deployment.zip`
3. Fill in the required store listing information
4. Submit for review

## Important Notes

- **Never submit a package with the "key" field included** - This will cause submission rejection
- **Always restore the key immediately after packaging** - This maintains development environment functionality
- **The images directory is excluded** - This reduces package size and upload time
- **Keep the deployment zip file** - You may need it for resubmission or updates

## Troubleshooting

### Common Issues:
1. **"Key field present" error**: Ensure the key was completely removed from manifest.json before packaging
2. **Package too large**: Verify images directory and other large files are excluded
3. **Development extension broken**: Ensure the key was properly restored after packaging

### Recovery:
If you forget to restore the key, add this line back to manifest.json at line 6:
```json
"key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAv+i/FGLF/2fYNW4EVu/zAXws9tUzQfnmMJQmgRozC1uDuC8/h8Kku+/TiC01yVePody1i+KuyNIVb7Hh6+G6RDtDRdDzCDlk/Ebn7SpT+B0nmWz83tzIj652OH78B7Z40oaeFOyczYkIpiqLgqeGM/gSfx5hyhSeJU5zn56Eja53GFwV5MTE3VTt2VhOiSbVm7bpTg5CoNMhqqaWtB3dnZoUWGJQG5X/ttozCfVVeY9LPj+LsDgD4A3eLnpOUT0vHN2lFOCHrbxPLOR5lQRyKwtUxiAFglpTQwiPpWv6kJKrj2lfLyW2MYT6o1eCVTp0hKblEPEJU9wPYZ/hoQx7jwIDAQAB",
```
