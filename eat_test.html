<md-card layout="" flex="" class="venue-card md-card _md layout-row flex" id="4392064" ng-init="venue = ctrl.venues.getVenue(venueId)" ng-repeat="venueId in ctrl.venues.displayedVenues" ng-style="ctrl.getCardStyle(venueId)" style="grid-row-end: span 253;">
    <div flex="" class="venue-card-container layout-column flex" layout="column" ng-class="{'loading': ctrl.venues.isLoadingMenu(venueId)}" style="">
      <div layout="" layout-align="center center" class="print-hidden cards-actions-wrapper layout-align-center-center layout-row">
        <!---->
        <!----><a class="md-icon-button location-button md-button md-ink-ripple" ng-transclude="" aria-label="'Open map view for Local'" md-ink-ripple="#fff" target="_blank" ng-href="https://campusmaps.googleplex.com/?q=US-SVL-HMBLT1-1-1B0" ng-if="venue | formatLocation: ctrl.venues.getSelectedMealDate(venueId)" href="https://campusmaps.googleplex.com/?q=US-SVL-HMBLT1-1-1B0">
          
          <md-icon class="md-icon material-icons" aria-hidden="true" role="img">place</md-icon>
        </a><!---->
        <!----><button class="md-icon-button share-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="'Get link to Local's menu'" md-ink-ripple="#fff" ng-click="ctrl.openShareDialog(venueId)" ng-if="!ctrl.venues.isShareView">
          
          <md-icon class="md-icon material-icons" aria-hidden="true" role="img">share</md-icon>
        </button><!---->
        <md-progress-circular class="venue-loader md-mode-indeterminate ng-hide" md-diameter="24" md-mode="indeterminate" ng-show="ctrl.venueLoadingStatuses[venueId]" aria-valuemin="0" aria-valuemax="100" role="progressbar" aria-hidden="true" style="width: 24px; height: 24px;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" style="width: 24px; height: 24px; transform-origin: 12px 12px 12px;"><path fill="none" stroke-width="2.4000000000000004" stroke-linecap="square" d="M12,1.2000000000000002A10.8,10.8 0 1 1 1.2000000000000002,12" stroke-dasharray="50.89380098815465" stroke-dashoffset="121.8004951650093" transform="rotate(-180 12 12)"></path></svg></md-progress-circular>
        <button class="md-icon-button favorite-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="Add Local to favorites" aria-pressed="true" aria-description="Local is in favorites" id="favorite-button-Local" md-ink-ripple="#fff" ng-class="{'loading-statuses': ctrl.venueLoadingStatuses[venueId]}" ng-click="ctrl.toggleVenueFavorite(venueId)">
          
          <md-icon class="md-icon material-icons" aria-hidden="true" role="img">
            favorite
          </md-icon>
        </button>
        <span flex="" class="flex"></span>
        <!----><button class="card-dismiss-button md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="Dismiss Local card" md-ink-ripple="#fff" ng-click="ctrl.dismissVenueCard(venueId)" ng-if="!ctrl.venues.isShareView">
          
          <md-icon class="md-icon material-icons" aria-hidden="true" role="img">close</md-icon>
        </button><!---->
      </div>
      <div class="venue-image-wrapper">
        <!----><div class="venue-image" style="background-image: url(https://lh3.googleusercontent.com/5Eo-ngzAjv9W2M0iA4Kds7zaepNlV7axmoSFwkx8KQcKQ-Vj4B4Z1cZiBLU6W5602NFN6hpP48kttU-UBlNXyVFrCh09OuPOArR9DPTAtQSMy5w=s0)" ng-if="venue.image[0]">
        </div><!---->
      </div>
      <div layout="" class="print-center venue-card-header layout-align-start-start layout-row" layout-align="start start">
        <div flex="" layout="column" class="layout-column flex">
          <h3 class="md-title" id="venue-name-4392064">Local</h3>




          <food-venue-card-details class="printable card-details venueDetailsComponent__printable" schedule="ctrl.venues.getSchedule(venueId)" selected-meal-date="ctrl.venues.getSelectedMealDate(venueId)" venue="venue"><div class="venueDetailsComponent">
<div class="schedule-container venueDetailsComponent__container layout-column flex" flex="" layout="column">
<!----><span class="description" ng-if="ctrl.venue.description">
  Enjoy a curated menu experience from Local culinarians, offering a variety of accompaniments that can be paired with simple and fresh ingredients.
</span><!---->
  <!----><a aria-label="SVL HMBLT1, Floor 1
242 Humboldt Court, Sunnyvale, CA 94089, US location" class="address md-ink-ripple" md-ink-ripple="" ng-href="https://campusmaps.googleplex.com/?q=US-SVL-HMBLT1-1-1B0" ng-if="ctrl.venue | formatLocation: ctrl.selectedMealDate" target="_blank" href="https://campusmaps.googleplex.com/?q=US-SVL-HMBLT1-1-1B0">
    <div layout="" class="layout-row">
      <div>
        <md-icon aria-hidden="true" class="venueDetailsComponent__icon material-icons" role="img">place</md-icon>
      </div>
      <div class="location-text">SVL HMBLT1, Floor 1
242 Humboldt Court, Sunnyvale, CA 94089, US</div>
    </div>
  </a><!---->
  <div class="schedule layout-row" layout="">
    <div>
      <md-icon aria-hidden="true" class="venueDetailsComponent__icon material-icons" role="img">schedule</md-icon>
    </div>
    <div>
      <div class="schedule-date">today, June 2</div>
      <!---->
      <div layout="column" class="layout-column">
        <!----><div ng-repeat="period in ctrl.dailySchedule">
          <div ng-class="{'green': ctrl.isServing(period) &amp;&amp; !period.isClosedForSpecialHours}" class="green">
            <span>Breakfast:</span>
            <!---->
            <!----><span ng-if="!period.isClosedForSpecialHours">7:30 am - 10:00 am</span><!---->
          </div>
        </div><!----><div ng-repeat="period in ctrl.dailySchedule">
          <div ng-class="{'green': ctrl.isServing(period) &amp;&amp; !period.isClosedForSpecialHours}">
            <span>Lunch:</span>
            <!---->
            <!----><span ng-if="!period.isClosedForSpecialHours">11:30 am - 2:00 pm</span><!---->
          </div>
        </div><!----><div ng-repeat="period in ctrl.dailySchedule">
          <div ng-class="{'green': ctrl.isServing(period) &amp;&amp; !period.isClosedForSpecialHours}">
            <span>Dinner:</span>
            <!---->
            <!----><span ng-if="!period.isClosedForSpecialHours">6:30 pm - 8:00 pm</span><!---->
          </div>
        </div><!---->
      </div>
    </div>
  </div>
  <div class="schedule-button">
    <button class="md-primary md-button md-ink-ripple" type="button" ng-transclude="" ng-click="ctrl.openWeeklySchedule($event)">
      View full schedule
    </button>
  </div>
</div>

</div>
</food-venue-card-details>
        </div>
        <div layout="column" layout-align="start end" class="status-and-more layout-align-start-end layout-column">
          <!----><food-status class="food-status layout-align-end-end layout-row" layout="" layout-align="end end" status="ctrl.venues.getStatus(venueId)" ng-if="ctrl.venues.getStatus(venueId)"><div class="foodStatusComponent">
<!----><div class="foodStatusComponent__container layout-row status-green" layout="row" ng-class="ctrl.getCssClassForStatus()" ng-if="ctrl.status">
  <div class="foodStatusComponent__container--content layout-row" layout="">
    <!----><span ng-repeat="statusPart in ctrl.status.statusTextParts" ng-class="{'bold': $index == ctrl.status.highlightAtIndex}" class="bold">
      1+&nbsp;
    </span><!----><span ng-repeat="statusPart in ctrl.status.statusTextParts" ng-class="{'bold': $index == ctrl.status.highlightAtIndex}">
      hours left&nbsp;
    </span><!---->
  </div>
</div><!---->
</div>
</food-status><!---->
          <button class="print-hidden details-toggle md-button md-ink-ripple" type="button" ng-transclude="" aria-describedby="venue-name-4392064" aria-expanded="false" role="button" ng-click="expanded = !expanded; ctrl.toggleVenueDetails(expanded)">
            More info
            <md-icon aria-hidden="true" class="md-icon material-icons" role="img">expand_more
            </md-icon>
          </button>
        </div>
      </div>
      <!---->
      <md-divider></md-divider>
      <!---->
      <!---->
      <!----><food-venue-card-menu class="dishMenuComponent layout-row flex" flex="" layout="" ng-if="ctrl.venues.getMenu(venueId)" menu="ctrl.venues.getMenu(venueId)" venue-id="venueId" style=""><div class="dishMenuComponent">
<div class="venue-station dishMenuComponent__container layout-column flex" flex="" layout="column">
  <!---->
  <!----><div class="venue-station-item" ng-repeat="station in ctrl.menu.stations" style="">
    <h4 aria-description="Local Curated Breakfast is expanded" aria-expanded="true" aria-label="Local Curated Breakfast" class="station-header layout-align-start-center layout-row" layout="" layout-align="start center" ng-click="ctrl.toggleCollapsedStation($event, station.stationId)" role="heading" tabindex="0">
      <span flex="" class="flex">Local Curated Breakfast</span>
      <!---->
      <md-icon aria-hidden="true" class="print-hidden material-icons" role="img">
        expand_less
      </md-icon>
    </h4>
    <!----><!----><div ng-if="!ctrl.isStationCollapsed(station.stationId)" ng-repeat="dish in station.dishList.dishes">
      <food-dish-item dish="dish" venue-id="ctrl.venueId"><div class="dishItemComponent">
<div aria-expanded="true" class="dish dishItemComponent__container expandable" ng-class="(ctrl.containsAvoidance() ? 'avoidance' : '')" role="presentation">
  <div class="venue-dish-header layout-row" layout="" ng-click="ctrl.toggleDish($event)" role="button" tabindex="0">
    <div class="venue-dish-title layout-row flex" flex="" layout="">
    <!---->
      <h5 aria-label="Buttermilk Pancake" class="dish-header-button flex" dir="ltr" flex="">
        Buttermilk Pancake
      </h5>
    </div>
    <!----><div class="diet-tags" ng-if="ctrl.showDiet()">
      <h6 class="food-invisible-text">Diet</h6>
      <!----><div class="diet-item Vegetarian layout-align-end-center layout-row" layout="" layout-align="end center" ng-repeat="tag in ctrl.dish.dietTags">
        Vegetarian
      </div><!---->
    </div><!---->
    <md-icon aria-hidden="true" class="dish-expand-button-icon material-icons" role="img">
      expand_less
    </md-icon>
  </div>
  <!----><div class="dish-details" ng-if="ctrl.isDishExpanded">
    <!----><div class="highlighting-reason" ng-if="ctrl.shouldShowRationales()">
      <div class="allergy-container">
      <!---->
        <!---->
        <!---->
      </div>
    </div><!---->
    <!----><div class="allergens selectable layout-align-start-start layout-row" layout="" layout-align="start start" ng-if="ctrl.shouldShowAllergens()">
      <div>
        <h6 class="dishItemComponent__allergens-title">Contains:&nbsp;</h6>
        Egg, Milk, Wheat
      </div>
    </div><!---->
    <div class="ingredients selectable layout-row" dir="ltr" layout="">
      <div class="ingredients-expand-button">
        <h6 class="dishItemComponent__ingredients-title">Ingredients:&nbsp;</h6>
      </div>
      <span class="ingredients-list" ng-class="{'separated': ctrl.dataStore.hasUserDeskCountryMigrated}">
        Buttermilk, 00 Flour, Liquid Egg, Sugar, Baking Soda, Baking Powder, Ground Cinnamon, Alcohol Free Vanilla, Rice Bran Oil, Kosher Salt
      </span>
    </div>
    <!----><div class="feedback-actions layout-row" layout="" ng-if="ctrl.shouldShowFeedbackActions()">
      <div class="feedback-action feedback-action-like layout-align-center-center layout-row" layout="" layout-align="center center">
        <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="0 likes" aria-pressed="false" ng-click="$event.stopPropagation(); ctrl.toggleLike($event)" tabindex="0">
          <md-icon aria-hidden="true" class="feedback-action__icon material-icons" ng-class="{active: ctrl.dish.isLiked}" role="img">
            thumb_up_off_alt
          </md-icon>
          
        </button>
        <!----><span aria-hidden="true" class="feedback-count" ng-if="!ctrl.isLoading">
        
      </span><!---->
        <!---->
      </div>
      <div class="feedback-action feedback-action-comment layout-align-center-center layout-row" layout="" layout-align="center center">
        <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="0 comments" ng-click="$event.stopPropagation(); ctrl.openDishCommentsDialog($event)" tabindex="0">
          <md-icon aria-hidden="true" class="feedback-action__icon material-icons" role="img">chat_bubble_outline</md-icon>
          
        </button>
        <span aria-hidden="true" class="feedback-count">
        
      </span>
      </div>
    </div><!---->
  </div><!---->
</div>
<md-divider class="venue-dish-divider"></md-divider>
</div>
</food-dish-item>
    </div><!----><!----><!----><div ng-if="!ctrl.isStationCollapsed(station.stationId)" ng-repeat="dish in station.dishList.dishes">
      <food-dish-item dish="dish" venue-id="ctrl.venueId"><div class="dishItemComponent">
<div aria-expanded="true" class="dish dishItemComponent__container expandable" ng-class="(ctrl.containsAvoidance() ? 'avoidance' : '')" role="presentation">
  <div class="venue-dish-header layout-row" layout="" ng-click="ctrl.toggleDish($event)" role="button" tabindex="0">
    <div class="venue-dish-title layout-row flex" flex="" layout="">
    <!---->
      <h5 aria-label="Mediterranean Vegetable Frittata" class="dish-header-button flex" dir="ltr" flex="">
        Mediterranean Vegetable Frittata
      </h5>
    </div>
    <!----><div class="diet-tags" ng-if="ctrl.showDiet()">
      <h6 class="food-invisible-text">Diet</h6>
      <!----><div class="diet-item Vegetarian layout-align-end-center layout-row" layout="" layout-align="end center" ng-repeat="tag in ctrl.dish.dietTags">
        Vegetarian
      </div><!---->
    </div><!---->
    <md-icon aria-hidden="true" class="dish-expand-button-icon material-icons" role="img">
      expand_less
    </md-icon>
  </div>
  <!----><div class="dish-details" ng-if="ctrl.isDishExpanded">
    <!----><div class="highlighting-reason" ng-if="ctrl.shouldShowRationales()">
      <div class="allergy-container">
      <!---->
        <!---->
        <!---->
      </div>
    </div><!---->
    <!----><div class="allergens selectable layout-align-start-start layout-row" layout="" layout-align="start start" ng-if="ctrl.shouldShowAllergens()">
      <div>
        <h6 class="dishItemComponent__allergens-title">Contains:&nbsp;</h6>
        Egg, Milk, Sulfite
      </div>
    </div><!---->
    <div class="ingredients selectable layout-row" dir="ltr" layout="">
      <div class="ingredients-expand-button">
        <h6 class="dishItemComponent__ingredients-title">Ingredients:&nbsp;</h6>
      </div>
      <span class="ingredients-list" ng-class="{'separated': ctrl.dataStore.hasUserDeskCountryMigrated}">
        Egg Frittata Mix (Liquid Egg, Kosher Salt, Heavy Cream), Roasted Bell Pepper And Mushroom (Bell Pepper, Button Mushroom, Extra Virgin Olive Oil, Kosher Salt), Spinach, Blistered Cherry Tomato (Cherry Tomato, Extra Virgin Olive Oil, Red Wine Vinegar, Oregano, Kosher Salt, Black Pepper), Shallot, Sun Dried Tomato, Kalamata Olive, Parsley, Pan Spray
      </span>
    </div>
    <!----><div class="feedback-actions layout-row" layout="" ng-if="ctrl.shouldShowFeedbackActions()">
      <div class="feedback-action feedback-action-like layout-align-center-center layout-row" layout="" layout-align="center center">
        <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="0 likes" aria-pressed="false" ng-click="$event.stopPropagation(); ctrl.toggleLike($event)" tabindex="0">
          <md-icon aria-hidden="true" class="feedback-action__icon material-icons" ng-class="{active: ctrl.dish.isLiked}" role="img">
            thumb_up_off_alt
          </md-icon>
          
        </button>
        <!----><span aria-hidden="true" class="feedback-count" ng-if="!ctrl.isLoading">
        
      </span><!---->
        <!---->
      </div>
      <div class="feedback-action feedback-action-comment layout-align-center-center layout-row" layout="" layout-align="center center">
        <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="0 comments" ng-click="$event.stopPropagation(); ctrl.openDishCommentsDialog($event)" tabindex="0">
          <md-icon aria-hidden="true" class="feedback-action__icon material-icons" role="img">chat_bubble_outline</md-icon>
          
        </button>
        <span aria-hidden="true" class="feedback-count">
        
      </span>
      </div>
    </div><!---->
  </div><!---->
</div>
<md-divider class="venue-dish-divider"></md-divider>
</div>
</food-dish-item>
    </div><!----><!----><!----><div ng-if="!ctrl.isStationCollapsed(station.stationId)" ng-repeat="dish in station.dishList.dishes">
      <food-dish-item dish="dish" venue-id="ctrl.venueId"><div class="dishItemComponent">
<div aria-expanded="true" class="dish dishItemComponent__container expandable" ng-class="(ctrl.containsAvoidance() ? 'avoidance' : '')" role="presentation">
  <div class="venue-dish-header layout-row" layout="" ng-click="ctrl.toggleDish($event)" role="button" tabindex="0">
    <div class="venue-dish-title layout-row flex" flex="" layout="">
    <!---->
      <h5 aria-label="Scrambled Egg Whites" class="dish-header-button flex" dir="ltr" flex="">
        Scrambled Egg Whites
      </h5>
    </div>
    <!----><div class="diet-tags" ng-if="ctrl.showDiet()">
      <h6 class="food-invisible-text">Diet</h6>
      <!----><div class="diet-item Vegetarian layout-align-end-center layout-row" layout="" layout-align="end center" ng-repeat="tag in ctrl.dish.dietTags">
        Vegetarian
      </div><!---->
    </div><!---->
    <md-icon aria-hidden="true" class="dish-expand-button-icon material-icons" role="img">
      expand_less
    </md-icon>
  </div>
  <!----><div class="dish-details" ng-if="ctrl.isDishExpanded">
    <!----><div class="highlighting-reason" ng-if="ctrl.shouldShowRationales()">
      <div class="allergy-container">
      <!---->
        <!---->
        <!---->
      </div>
    </div><!---->
    <!----><div class="allergens selectable layout-align-start-start layout-row" layout="" layout-align="start start" ng-if="ctrl.shouldShowAllergens()">
      <div>
        <h6 class="dishItemComponent__allergens-title">Contains:&nbsp;</h6>
        Egg
      </div>
    </div><!---->
    <div class="ingredients selectable layout-row" dir="ltr" layout="">
      <div class="ingredients-expand-button">
        <h6 class="dishItemComponent__ingredients-title">Ingredients:&nbsp;</h6>
      </div>
      <span class="ingredients-list" ng-class="{'separated': ctrl.dataStore.hasUserDeskCountryMigrated}">
        Liquid Egg White, Kosher Salt, Pan Spray
      </span>
    </div>
    <!----><div class="feedback-actions layout-row" layout="" ng-if="ctrl.shouldShowFeedbackActions()">
      <div class="feedback-action feedback-action-like layout-align-center-center layout-row" layout="" layout-align="center center">
        <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="0 likes" aria-pressed="false" ng-click="$event.stopPropagation(); ctrl.toggleLike($event)" tabindex="0">
          <md-icon aria-hidden="true" class="feedback-action__icon material-icons" ng-class="{active: ctrl.dish.isLiked}" role="img">
            thumb_up_off_alt
          </md-icon>
          
        </button>
        <!----><span aria-hidden="true" class="feedback-count" ng-if="!ctrl.isLoading">
        
      </span><!---->
        <!---->
      </div>
      <div class="feedback-action feedback-action-comment layout-align-center-center layout-row" layout="" layout-align="center center">
        <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="0 comments" ng-click="$event.stopPropagation(); ctrl.openDishCommentsDialog($event)" tabindex="0">
          <md-icon aria-hidden="true" class="feedback-action__icon material-icons" role="img">chat_bubble_outline</md-icon>
          
        </button>
        <span aria-hidden="true" class="feedback-count">
        
      </span>
      </div>
    </div><!---->
  </div><!---->
</div>
<md-divider class="venue-dish-divider"></md-divider>
</div>
</food-dish-item>
    </div><!----><!----><!----><div ng-if="!ctrl.isStationCollapsed(station.stationId)" ng-repeat="dish in station.dishList.dishes">
      <food-dish-item dish="dish" venue-id="ctrl.venueId"><div class="dishItemComponent">
<div aria-expanded="true" class="dish dishItemComponent__container expandable" ng-class="(ctrl.containsAvoidance() ? 'avoidance' : '')" role="presentation">
  <div class="venue-dish-header layout-row" layout="" ng-click="ctrl.toggleDish($event)" role="button" tabindex="0">
    <div class="venue-dish-title layout-row flex" flex="" layout="">
    <!---->
      <h5 aria-label="Sauteed Spinach" class="dish-header-button flex" dir="ltr" flex="">
        Sauteed Spinach
      </h5>
    </div>
    <!----><div class="diet-tags" ng-if="ctrl.showDiet()">
      <h6 class="food-invisible-text">Diet</h6>
      <!----><div class="diet-item Vegan layout-align-end-center layout-row" layout="" layout-align="end center" ng-repeat="tag in ctrl.dish.dietTags">
        Vegan
      </div><!---->
    </div><!---->
    <md-icon aria-hidden="true" class="dish-expand-button-icon material-icons" role="img">
      expand_less
    </md-icon>
  </div>
  <!----><div class="dish-details" ng-if="ctrl.isDishExpanded">
    <!----><div class="highlighting-reason" ng-if="ctrl.shouldShowRationales()">
      <div class="allergy-container">
      <!---->
        <!---->
        <!---->
      </div>
    </div><!---->
    <!---->
    <div class="ingredients selectable layout-row" dir="ltr" layout="">
      <div class="ingredients-expand-button">
        <h6 class="dishItemComponent__ingredients-title">Ingredients:&nbsp;</h6>
      </div>
      <span class="ingredients-list" ng-class="{'separated': ctrl.dataStore.hasUserDeskCountryMigrated}">
        Spinach, Rice Bran Oil, Kosher Salt, Black Pepper
      </span>
    </div>
    <!----><div class="feedback-actions layout-row" layout="" ng-if="ctrl.shouldShowFeedbackActions()">
      <div class="feedback-action feedback-action-like layout-align-center-center layout-row" layout="" layout-align="center center">
        <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="0 likes" aria-pressed="false" ng-click="$event.stopPropagation(); ctrl.toggleLike($event)" tabindex="0">
          <md-icon aria-hidden="true" class="feedback-action__icon material-icons" ng-class="{active: ctrl.dish.isLiked}" role="img">
            thumb_up_off_alt
          </md-icon>
          
        </button>
        <!----><span aria-hidden="true" class="feedback-count" ng-if="!ctrl.isLoading">
        
      </span><!---->
        <!---->
      </div>
      <div class="feedback-action feedback-action-comment layout-align-center-center layout-row" layout="" layout-align="center center">
        <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="0 comments" ng-click="$event.stopPropagation(); ctrl.openDishCommentsDialog($event)" tabindex="0">
          <md-icon aria-hidden="true" class="feedback-action__icon material-icons" role="img">chat_bubble_outline</md-icon>
          
        </button>
        <span aria-hidden="true" class="feedback-count">
        
      </span>
      </div>
    </div><!---->
  </div><!---->
</div>
<md-divider class="venue-dish-divider"></md-divider>
</div>
</food-dish-item>
    </div><!----><!---->
    <!---->
  </div><!----><div class="venue-station-item" ng-repeat="station in ctrl.menu.stations">
    <h4 aria-description="Local Breakfast Cereals is expanded" aria-expanded="true" aria-label="Local Breakfast Cereals" class="station-header layout-align-start-center layout-row" layout="" layout-align="start center" ng-click="ctrl.toggleCollapsedStation($event, station.stationId)" role="heading" tabindex="0">
      <span flex="" class="flex">Local Breakfast Cereals</span>
      <!---->
      <md-icon aria-hidden="true" class="print-hidden material-icons" role="img">
        expand_less
      </md-icon>
    </h4>
    <!----><!----><div ng-if="!ctrl.isStationCollapsed(station.stationId)" ng-repeat="dish in station.dishList.dishes">
      <food-dish-item dish="dish" venue-id="ctrl.venueId"><div class="dishItemComponent">
<div aria-expanded="true" class="dish dishItemComponent__container expandable" ng-class="(ctrl.containsAvoidance() ? 'avoidance' : '')" role="presentation">
  <div class="venue-dish-header layout-row" layout="" ng-click="ctrl.toggleDish($event)" role="button" tabindex="0">
    <div class="venue-dish-title layout-row flex" flex="" layout="">
    <!---->
      <h5 aria-label="Steel Cut Oat" class="dish-header-button flex" dir="ltr" flex="">
        Steel Cut Oat
      </h5>
    </div>
    <!----><div class="diet-tags" ng-if="ctrl.showDiet()">
      <h6 class="food-invisible-text">Diet</h6>
      <!----><div class="diet-item Vegan layout-align-end-center layout-row" layout="" layout-align="end center" ng-repeat="tag in ctrl.dish.dietTags">
        Vegan
      </div><!---->
    </div><!---->
    <md-icon aria-hidden="true" class="dish-expand-button-icon material-icons" role="img">
      expand_less
    </md-icon>
  </div>
  <!----><div class="dish-details" ng-if="ctrl.isDishExpanded">
    <!----><div class="highlighting-reason" ng-if="ctrl.shouldShowRationales()">
      <div class="allergy-container">
      <!---->
        <!---->
        <!---->
      </div>
    </div><!---->
    <!---->
    <div class="ingredients selectable layout-row" dir="ltr" layout="">
      <div class="ingredients-expand-button">
        <h6 class="dishItemComponent__ingredients-title">Ingredients:&nbsp;</h6>
      </div>
      <span class="ingredients-list" ng-class="{'separated': ctrl.dataStore.hasUserDeskCountryMigrated}">
        Steel Cut Oat
      </span>
    </div>
    <!----><div class="feedback-actions layout-row" layout="" ng-if="ctrl.shouldShowFeedbackActions()">
      <div class="feedback-action feedback-action-like layout-align-center-center layout-row" layout="" layout-align="center center">
        <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="2 likes" aria-pressed="false" ng-click="$event.stopPropagation(); ctrl.toggleLike($event)" tabindex="0">
          <md-icon aria-hidden="true" class="feedback-action__icon material-icons" ng-class="{active: ctrl.dish.isLiked}" role="img">
            thumb_up_off_alt
          </md-icon>
          
        </button>
        <!----><span aria-hidden="true" class="feedback-count" ng-if="!ctrl.isLoading">
        2
      </span><!---->
        <!---->
      </div>
      <div class="feedback-action feedback-action-comment layout-align-center-center layout-row" layout="" layout-align="center center">
        <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="0 comments" ng-click="$event.stopPropagation(); ctrl.openDishCommentsDialog($event)" tabindex="0">
          <md-icon aria-hidden="true" class="feedback-action__icon material-icons" role="img">chat_bubble_outline</md-icon>
          
        </button>
        <span aria-hidden="true" class="feedback-count">
        
      </span>
      </div>
    </div><!---->
  </div><!---->
</div>
<md-divider class="venue-dish-divider"></md-divider>
</div>
</food-dish-item>
    </div><!----><!---->
    <!---->
  </div><!----><div class="venue-station-item" ng-repeat="station in ctrl.menu.stations">
    <h4 aria-description="Express Breakfast Sandwiches is expanded" aria-expanded="true" aria-label="Express Breakfast Sandwiches" class="station-header layout-align-start-center layout-row" layout="" layout-align="start center" ng-click="ctrl.toggleCollapsedStation($event, station.stationId)" role="heading" tabindex="0">
      <span flex="" class="flex">Express Breakfast Sandwiches</span>
      <!---->
      <md-icon aria-hidden="true" class="print-hidden material-icons" role="img">
        expand_less
      </md-icon>
    </h4>
    <!----><!----><div ng-if="!ctrl.isStationCollapsed(station.stationId)" ng-repeat="dish in station.dishList.dishes">
      <food-dish-item dish="dish" venue-id="ctrl.venueId"><div class="dishItemComponent">
<div aria-expanded="true" class="dish dishItemComponent__container expandable" ng-class="(ctrl.containsAvoidance() ? 'avoidance' : '')" role="presentation">
  <div class="venue-dish-header layout-row" layout="" ng-click="ctrl.toggleDish($event)" role="button" tabindex="0">
    <div class="venue-dish-title layout-row flex" flex="" layout="">
    <!---->
      <h5 aria-label="Egg &amp; Cheese English Muffin Sandwich" class="dish-header-button flex" dir="ltr" flex="">
        Egg &amp; Cheese English Muffin Sandwich
      </h5>
    </div>
    <!----><div class="diet-tags" ng-if="ctrl.showDiet()">
      <h6 class="food-invisible-text">Diet</h6>
      <!----><div class="diet-item Vegetarian layout-align-end-center layout-row" layout="" layout-align="end center" ng-repeat="tag in ctrl.dish.dietTags">
        Vegetarian
      </div><!---->
    </div><!---->
    <md-icon aria-hidden="true" class="dish-expand-button-icon material-icons" role="img">
      expand_less
    </md-icon>
  </div>
  <!----><div class="dish-details" ng-if="ctrl.isDishExpanded">
    <!----><div class="highlighting-reason" ng-if="ctrl.shouldShowRationales()">
      <div class="allergy-container">
      <!---->
        <!---->
        <!---->
      </div>
    </div><!---->
    <!----><div class="allergens selectable layout-align-start-start layout-row" layout="" layout-align="start start" ng-if="ctrl.shouldShowAllergens()">
      <div>
        <h6 class="dishItemComponent__allergens-title">Contains:&nbsp;</h6>
        Egg, Milk, Soybeans, Sulfate, Wheat
      </div>
    </div><!---->
    <div class="ingredients selectable layout-row" dir="ltr" layout="">
      <div class="ingredients-expand-button">
        <h6 class="dishItemComponent__ingredients-title">Ingredients:&nbsp;</h6>
      </div>
      <span class="ingredients-list" ng-class="{'separated': ctrl.dataStore.hasUserDeskCountryMigrated}">
        Egg Patty (Liquid Egg, Heavy Cream, Kosher Salt, Pan Spray), English Muffin, Cheddar Cheese
      </span>
    </div>
    <!----><div class="feedback-actions layout-row" layout="" ng-if="ctrl.shouldShowFeedbackActions()">
      <div class="feedback-action feedback-action-like layout-align-center-center layout-row" layout="" layout-align="center center">
        <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="0 likes" aria-pressed="false" ng-click="$event.stopPropagation(); ctrl.toggleLike($event)" tabindex="0">
          <md-icon aria-hidden="true" class="feedback-action__icon material-icons" ng-class="{active: ctrl.dish.isLiked}" role="img">
            thumb_up_off_alt
          </md-icon>
          
        </button>
        <!----><span aria-hidden="true" class="feedback-count" ng-if="!ctrl.isLoading">
        
      </span><!---->
        <!---->
      </div>
      <div class="feedback-action feedback-action-comment layout-align-center-center layout-row" layout="" layout-align="center center">
        <button class="md-icon-button md-button md-ink-ripple" type="button" ng-transclude="" aria-label="0 comments" ng-click="$event.stopPropagation(); ctrl.openDishCommentsDialog($event)" tabindex="0">
          <md-icon aria-hidden="true" class="feedback-action__icon material-icons" role="img">chat_bubble_outline</md-icon>
          
        </button>
        <span aria-hidden="true" class="feedback-count">
        
      </span>
      </div>
    </div><!---->
  </div><!---->
</div>
<md-divider class="venue-dish-divider"></md-divider>
</div>
</food-dish-item>
    </div><!----><!---->
    <!---->
  </div><!---->
  <!----><div class="discouraged-dishes-header print-hidden layout-align-start-center layout-row" layout="" layout-align="none center" ng-if="ctrl.menu.hasHiddenDishes()" style="">
    <h4 ng-class="{'hide-show-all-button': ctrl.showDiscouragedDishes || !ctrl.showDiscouragedDishes}" class="hide-show-all-button">
      2 dishes filtered out (from Eater Profile)
    </h4>
    <span flex="" class="flex"></span>
    <button class="md-button md-ink-ripple" type="button" ng-transclude="" aria-expanded="false" ng-click="ctrl.showDiscouragedDishes = !ctrl.showDiscouragedDishes">
      Show all
    </button>
  </div><!---->
  <!----><!----><!----><!----><!---->
</div>
</div>
</food-venue-card-menu><!---->
    </div>
    <!---->
  </md-card>